import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout'
import React from 'react'
import DataTable from 'react-data-table-component';
import { MdDelete } from "react-icons/md";
import toast from 'react-hot-toast';
import { Head, router,Link } from '@inertiajs/react';
import DeleteConfirmationModal from '@/Components/Dashboard/DeleteModal';

const Index = ({users}) => {
    const [isModalOpen, setIsModalOpen] = React.useState(false);
    const [deleteId, setDeleteId] = React.useState(null);
    console.log("users",users)
    const columns = [
        {
            name: '<PERSON>a',
            selector: row => row.name,
            sortable: true
        },
        {
            name: 'Email',
            selector: row => row.email,
            sortable: true
        },
        {
            name: 'Created At',
            selector: row => row.created_at,
            sortable: true
        },
        {
            name: 'Operations',
            cell: row => (
                <div className="flex justify-center items-center">
                    <button
                        onClick={() => handleDelete(row.id)}
                        className="px-2 py-1 text-white bg-red-500 rounded hover:bg-red-700"
                    >
                        <MdDelete/>
                    </button>
                    <DeleteConfirmationModal
                        isOpen={isModalOpen}
                        onRequestClose={() => setIsModalOpen(false)}
                        onConfirm={handleConfirmDelete}
                        id={deleteId}
                        message="Apakah anda yakin ingin menghapus akun ini?"
                    />
                </div>
            ),
        },
    ];
    const handleDelete = (id) => {
        setDeleteId(id);
        setIsModalOpen(true);
    }

    const handleConfirmDelete = (id) => {
        // Menampilkan toast pertama
        toast.promise(
            new Promise((resolve, reject) => {
                router.delete(`/user/${id}`, {
                    onSuccess: () => {
                        resolve(); // Sukses, lanjut ke toast kedua
                    },
                    onError: (error) => {
                        reject(error); // Gagal, tampilkan error
                    }
                });
            }),
            {
                loading: 'Menghapus akun...',
                success: 'Akun berhasil dihapus!',
                error: (error) => `Terjadi kesalahan: ${error}`,
            }
        );
    };
    const paginationComponentOptions = {
        selectAllRowsItem: true,
        selectAllRowsItemText: "ALL"
    };
  return (
    <AuthenticatedLayout
    header={
        <h2 className="text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200">
            User Management
        </h2>
    }
    >
        <div className="py-0 lg:py-12">
                <div className="mx-auto max-w-7xl sm:px-6 lg:px-8">
                    <div className="overflow-hidden bg-white shadow-sm sm:rounded-lg dark:bg-gray-800">
                        <div className="p-6 text-gray-900 dark:text-gray-100">
                            <div className='flex justify-between gap-2 mb-5'>
                                <div className='bg-blue-500 text-white py-2 px-4 rounded w-[150px] h-10'>
                                    <Link href='/user/create'>Tambah User</Link>
                                </div>
                            </div>
                            <DataTable
                                columns={columns}
                                data={users}
                                pagination
                                paginationComponentOptions={paginationComponentOptions}
                                paginationTotalRows={users.length}
                                responsive
                                />
                        </div>
                    </div>
                </div>
            </div>
    </AuthenticatedLayout>
  )
}

export default Index