<?php

namespace App\Http\Controllers;

use App\Models\Keluarga;
use App\Models\Penduduk;
use Inertia\Inertia;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB; 

class DashboardController extends Controller
{
    public function jumlahPenduduk()
    {
        return Inertia::render('Dashboard', [
            'jumlahPenduduk' => $this->getTotalPenduduk(),
            'jumlahKeluarga' => $this->getTotalKeluarga(),
            'lakiLaki' => $this->getJumlahLakiLaki(),
            'perempuan' => $this->getJumlahPerempuan(),
            'pekerjaan' => $this->getJumlahPendudukBekerja(),
            'usiaData' => $this->getPendudukBerdasarkanUsia(),
            'rataRataUsia' => $this->getRataRataUsiaHidup(),
        ]);
    }

    private function getTotalPenduduk()
    {
        return Penduduk::count();
    }
    private function getTotalKeluarga()
    {
        return Keluarga::with('penduduk')
        ->where('no_kk', '!=', '0000000000000000')
        ->whereNotNull('deleted_at')
        ->count();
    }

    private function getJumlahLakiLaki()
    {
        return Penduduk::where('kelamin', 'Laki-laki')->count();
    }

    private function getJumlahPerempuan()
    {
        return Penduduk::where('kelamin', 'Perempuan')->count();
    }

    private function getJumlahPendudukBekerja()
    {
        return Penduduk::whereNotNull('pekerjaan')
            ->whereNotIn('pekerjaan', ['Belum / Tidak Bekerja', 'Mengurus Rumah Tangga', 'Pelajar / Mahasiswa', 'Pensiunan'])
            ->count();
    }

    private function getPendudukBerdasarkanUsia()
    {
        $rentangUsia = [
            ['name' => '1-16', 'min' => 1, 'max' => 16],
            ['name' => '17-25', 'min' => 17, 'max' => 25],
            ['name' => '26-35', 'min' => 26, 'max' => 35],
            ['name' => '36-45', 'min' => 36, 'max' => 45],
            ['name' => '46-60', 'min' => 46, 'max' => 60],
            ['name' => '>60', 'min' => 61, 'max' => null],
        ];

        $data = [];

        foreach ($rentangUsia as $rentang) {
            $query = Penduduk::query();

            // Hitung usia berdasarkan tanggal lahir menggunakan TIMESTAMPDIFF
            if ($rentang['max']) {
                $query->whereBetween(
                    DB::raw('TIMESTAMPDIFF(YEAR, tanggal, CURDATE())'),
                    [$rentang['min'], $rentang['max']]
                );
            } else {
                $query->where(
                    DB::raw('TIMESTAMPDIFF(YEAR, tanggal, CURDATE())'),
                    '>=',
                    $rentang['min']
                );
            }

            $jumlah = $query->count();

            $data[] = [
                'name' => $rentang['name'],
                'jumlah' => $jumlah,
            ];
        }

        return $data;
    }
    private function getRataRataUsiaHidup()
    {
        $penduduk = Penduduk::all();

        $totalUsia = $penduduk->sum(function ($item) {
            $lahirTahun = Carbon::parse($item->tanggal)->year;
            $sekarangTahun = Carbon::now()->year;
            return $sekarangTahun - $lahirTahun;
        });

        $jumlahPenduduk = $penduduk->count();

        $rataRataUsia = $jumlahPenduduk > 0 ? $totalUsia / $jumlahPenduduk : 0;

        // Membulatkan rata-rata usia
        return round($rataRataUsia);
    }
}