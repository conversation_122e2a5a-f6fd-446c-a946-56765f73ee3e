import React from 'react'

const DetailKTP = ({selectedData, closeModal}) => {
  console.log(selectedData.penduduk)
  return (
    <div>
      {/* <PERSON><PERSON><PERSON> dan <PERSON> */}
      <div className="text-center">
        <div className="text-xl font-semibold">PROVINSI DAERAH ISTIMEWA YOGYAKARTA</div>
        <div className="text-xl font-semibold">KABUPATEN SLEMAN</div>
      </div>
      <div className='pl-10'>
        <tr className="leading-none">
                    <td className="text-xl">NIK</td>
                    <td className="pl-24">:</td>
                    <td className="pl-2">{selectedData.nik}</td>
        </tr>
      </div>
      {/* Konten Tabel dan Gambar Samping */}
      <div className="flex  items-start mt-4 w-full">

              <table className="text-base  w-1/2">
                <tbody>
                  <tr className="leading-none">
                    <td className="text-base">Nama</td>
                    <td className="pl-14">:</td>
                    <td className="pl-1">{selectedData.nama}</td>
                  </tr>
                  <tr className="leading-none">
                    <td className="text-base">Tempat/Tgl Lahir</td>
                    <td className="pl-14">:</td>
                    <td className="pl-1">{selectedData.lahir}, {selectedData.tanggal}</td>
                  </tr>
                  <tr className="leading-none">
                    <td className="text-base">Alamat</td>
                    <td className="pl-14">:</td>
                    <td className="pl-1">{selectedData.alamat}</td>
                  </tr>
                  <tr className="leading-none">
                    <td className="pl-14 text-base">RT/RW</td>
                    <td className="pl-14">:</td>
                    <td className="pl-1">{selectedData.rt}/{selectedData.rw}</td>
                  </tr>
                  <tr className="leading-none">
                    <td className="pl-14 text-base">Kel/Desa</td>
                    <td className="pl-14">:</td>
                    <td className="pl-1"></td>
                  </tr>
                  <tr className="leading-none">
                    <td className="pl-14 text-base">Kecamatan</td>
                    <td className="pl-14">:</td>
                    <td className="pl-1">{selectedData.kecamatan}</td>
                  </tr>
                  <tr className="leading-none">
                    <td className="text-base">Agama</td>
                    <td className="pl-14">:</td>
                    <td className="pl-1">{selectedData.agama}</td>
                  </tr>
                  <tr className="leading-none">
                    <td className="text-base">Status Perkawinan</td>
                    <td className="pl-14">:</td>
                    <td className="pl-1">{selectedData.kawin}</td>
                  </tr>
                  <tr className="leading-none">
                    <td className="text-base">Pekerjaan</td>
                    <td className="pl-14">:</td>
                    <td className="pl-1">{selectedData.pekerjaan}</td>
                  </tr>
                  <tr className="leading-none">
                    <td className="text-base">Kewarganegaraan</td>
                    <td className="pl-14">:</td>
                    <td className="pl-1">{selectedData.kewarganegaran}</td>
                  </tr>
                  <tr className="leading-none">
                    <td className="text-base">Berlaku Hingga</td>
                    <td className="pl-14">:</td>
                    <td className="pl-1">SEUMUR HIDUP</td>
                  </tr>
                </tbody>
              </table>
          

    
        {/* Gambar di samping kanan */}
        <div className="ml-4 flex justify-center items-center w-1/2">
          <div className="flex flex-col items-center">
            <img 
              src="https://i.pinimg.com/736x/87/38/1f/87381fc5f84958b5220c81ab0d3228cc.jpg"  // Ganti dengan URL gambar online
              alt="Foto KTP"
              className="w-40 h-56 object-cover " // Ukuran gambar bisa disesuaikan
            />
            {/* Teks di bawah gambar */}
            <div className="mt-2 text-center text-sm">SLEMAN</div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default DetailKTP
