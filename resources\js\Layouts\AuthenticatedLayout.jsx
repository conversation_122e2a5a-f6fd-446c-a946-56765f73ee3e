import ApplicationLogo from '@/Components/ApplicationLogo';
import Dropdown from '@/Components/Dropdown';
import NavLink from '@/Components/NavLink';
import { Link, usePage } from '@inertiajs/react';
import { useState, useEffect } from 'react';

export default function AuthenticatedLayout({ header, children }) {
    const user = usePage().props.auth.user;
    const [showingSidebar, setShowingSidebar] = useState(false);
    const [isDropdownOpen, setIsDropdownOpen] = useState(false);

    const toggleDropdown = () => {
        setIsDropdownOpen(!isDropdownOpen);
    };
    const [showingNavigationDropdown, setShowingNavigationDropdown] =
        useState(false);
        const [darkMode, setDarkMode] = useState(() => {
            return localStorage.getItem('authDarkMode') === 'true' || false;
        });
    
        useEffect(() => {
            if (darkMode) {
                document.documentElement.classList.add('dark');
            } else {
                document.documentElement.classList.remove('dark');
            }
            localStorage.setItem('authDarkMode', darkMode);
        }, [darkMode]);
    return (
        <div className="h-screen bg-gray-100 dark:bg-gray-900 grid grid-cols-1 lg:flex">
            {/* Sidebar */}
            <div
                className={`${
                    showingSidebar ? 'block' : 'hidden'
                } sm:block sm:w-[100%] lg:w-64 bg-white dark:bg-gray-800 border-r border-gray-100 dark:border-gray-700 z-10`}
            >
                <div className="h-16 flex items-center px-4 border-b border-gray-200 dark:border-gray-700">
                    <Link href="/">
                        <ApplicationLogo className="block h-9 w-auto rounded-full object-cover fill-current text-gray-800 dark:text-gray-200" />
                    </Link>
                    <div className="ml-16 lg:ml-5 text-gray-800 dark:text-gray-200 font-bold text-sm">
                        Bale Mulia Residence
                    </div>
                </div>
                <nav className="mt-4 space-y-1 flex flex-col gap-3">
                    <NavLink
                        href={route('dashboard')}
                        active={route().current('dashboard')}
                    >
                        Dashboard
                    </NavLink>
                    <NavLink
                        href={route('kk.index')}
                        active={route().current('kk.index')}
                    >
                        Kartu Keluarga
                    </NavLink>
                    <NavLink
                        href={route('penduduk')}
                        active={route().current('penduduk')}
                    >
                        Penduduk
                    </NavLink>
                    <div className="relative">
                            <button
                                onClick={toggleDropdown}
                                className="inline-flex items-center pl-5 pt-1 text-sm  lg:text-lg  font-medium leading-5 transition duration-150 ease-in-out focus:outline-none border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 focus:border-gray-300 focus:text-gray-700 dark:text-gray-400 dark:hover:border-gray-700 dark:hover:text-gray-300 dark:focus:border-gray-700 dark:focus:text-gray-300"
                            >
                                Trash
                            </button>
                            {isDropdownOpen && (
                                <div className="absolute right-0 mt-2 w-48 bg-white text-gray-800 shadow-lg rounded dark:bg-gray-700 flex flex-col gap-2    ">
                                    <NavLink
                                        href={route("trash.keluarga")}
                                        className="block px-4 py-2 hover:bg-gray-400 w-full dark:bg-gray-400 rounded-sm dark:text-gray-800"
                                    >
                                        Kartu Keluarga
                                    </NavLink>
                                    <NavLink
                                        href={route("trash.penduduk")}
                                        className="block px-4 py-2 hover:bg-gray-100 w-full dark:bg-gray-400 rounded-sm dark:text-gray-800"
                                    >
                                        Penduduk
                                    </NavLink>
                                    
                                </div>
                            )}
                        </div>
                        {user.name === "dapek" && (
                                        <NavLink
                                        href={route('user.index')}
                                        active={route().current('user.index')}
                                        >
                                            User
                                        </NavLink>
                        )}
                </nav>
            </div>

            {/* Main Content */}
            <div className="flex-1 flex flex-col">
                {/* Topbar */}
                <nav className="h-16 bg-white dark:bg-gray-800 border-b border-gray-100 dark:border-gray-700 flex items-center justify-between px-4 sm:px-6 lg:px-8">
                    <button
                        onClick={() => setShowingSidebar(!showingSidebar)}
                        className="sm:hidden inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:bg-gray-100 hover:text-gray-500 dark:hover:bg-gray-900 dark:text-gray-500 dark:hover:text-gray-400"
                    >
                        {showingSidebar ? (
                            // Icon "X"
                            <svg
                                className="h-6 w-6"
                                stroke="currentColor"
                                fill="none"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth="2"
                                    d="M6 18L18 6M6 6l12 12"
                                />
                            </svg>
                        ) : (
                            // Icon Hamburger
                            <svg
                                className="h-6 w-6"
                                stroke="currentColor"
                                fill="none"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth="2"
                                    d="M4 6h16M4 12h16M4 18h16"
                                />
                            </svg>
                        )}
                    </button>

                    <div className="flex gap-2 items-center lg:order-2 w-full">
                        <div className='flex justify-between w-full'>
                            <div>
                                {header}
                            </div>
                            <div className='flex items-center gap-2'>
                                <button
                                    onClick={() => setDarkMode(!darkMode)}
                                    className="flex items-center justify-between text-sm text-gray-700 dark:text-gray-200"
                                >                
                                    <span   className="">                                       {darkMode ? '🌞' : '🌙'}
                                    </span>
                                </button>
                                <div className='flex items-center'>
                                    <span className="sm:inline text-gray-800 dark:text-gray-200">
                                        {user.name}
                                    </span>
                                    <Dropdown>
                                        <Dropdown.Trigger>
                                            <button className="flex items-center text-sm font-medium leading-4 text-gray-500 hover:text-gray-700 focus:outline-none dark:text-gray-400 dark:hover:text-gray-300">
                                                <svg
                                                    className="h-4 w-4"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    viewBox="0 0 20 20"
                                                    fill="currentColor"
                                                >
                                                    <path
                                                        fillRule="evenodd"
                                                        d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                                        clipRule="evenodd"
                                                    />
                                                </svg>
                                            </button>
                                        </Dropdown.Trigger>
                                        <Dropdown.Content>
                                            <Dropdown.Link href={route('profile.edit')}>
                                                Profile
                                            </Dropdown.Link>
                                            <Dropdown.Link href={route('logout')} method="post">
                                                Log Out
                                            </Dropdown.Link>
                                        </Dropdown.Content>
                                    </Dropdown>
                                </div>
                            </div>
                        </div>
                    </div>
                </nav>

                {/* Main content */}
                <main
                    className={`flex-1 px-4 sm:px-6 lg:px-8 overflow-y-auto max-h-screen ${
                        showingSidebar ? 'hidden sm:block' : ''
                    }`}
                >
                    {children}
                </main>
            </div>  
        </div>
    );
}
