<?php

namespace App\Http\Controllers;
use Inertia\Inertia;

use App\Models\Penduduk;
use App\Models\Keluarga;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class PendudukController extends Controller
{
    /**
     * Display a listing of the resource.protected $table = 'penduduks';
     */
    public function index()
    {
        //
        $penduduks = Penduduk::whereNull('deleted_at')->get();

        // Menambahkan kolom usia ke dalam data
        $penduduks = $penduduks->map(function ($item) {
            // Menghitung usia berdasarkan tahun lahir dan tahun sekarang
            $lahirTahun = Carbon::parse($item->tanggal)->year; // Mengambil tahun lahir
            $sekarangTahun = Carbon::now()->year; // Mengambil tahun sekarang
            $item->usia = $sekarangTahun - $lahirTahun; // Menghitung usia
            return $item;
        });
        

        return Inertia::render('Penduduk/Index', [
            'penduduk' => $penduduks,
        ]);
    }

    public function create()
    {
        return Inertia::render('Penduduk/Create',[
            'keluargas' => Keluarga::all(),
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
{
    // Validasi inputan terlebih dahulu
    $request->validate([
        'nik' => 'required|unique:penduduk|max:16',
        'nama' => 'required|string|max:255',
        'lahir' => 'required|string|max:255',
        'tanggal' => 'required|date',
        'kelamin' => 'required|string|max:255',
        'agama' => 'required|string|max:255',
        'kawin' => 'nullable|string|max:255',
        'pendidikan' => 'required|string|max:255',
        'pekerjaan' => 'required|string|max:255',
        'darah' => 'required|string|max:255',
        'kewarganegaran' => 'required|string|max:255',
        'hubungan' => 'nullable|string|max:255',
        'keluarga_id' => 'required|exists:keluargas,id',
        'telepon' => 'string|max:255',
        'alamat' => 'required|string|max:255',
    ]);

    // Menggunakan query builder untuk menyimpan data
    DB::table('penduduks')->insert([
        'nik' => $request->input('nik'),
        'nama' => $request->input('nama'),
        'lahir' => $request->input('lahir'),
        'tanggal' => $request->input('tanggal'),
        'kelamin' => $request->input('kelamin'),
        'agama' => $request->input('agama'),
        'kawin' => $request->input('kawin'),
        'pendidikan' => $request->input('pendidikan'),
        'pekerjaan' => $request->input('pekerjaan'),
        'darah' => $request->input('darah'),
        'kewarganegaran' => $request->input('kewarganegaran'),
        'hubungan' => $request->input('hubungan'),
        'keluarga_id' => $request->input('keluarga_id'),
        'telepon' => $request->input('telepon'),
        'alamat' => $request->input('alamat'),
        'created_at' => now(),  // Menambahkan timestamp
        'updated_at' => now(),  // Menambahkan timestamp
    ]);

    // Redirect dengan pesan sukses
    return redirect()->back()->with('message', 'Data berhasil disimpan!');
}
    /**
     * Update the specified resource in storage.
     */

     public function edit(Penduduk $penduduk)
     {
         return Inertia::render('Penduduk/Edit', ['penduduk' => $penduduk,'keluargas' => Keluarga::all(),]);
     }
    public function update(Request $request, Penduduk $penduduk)
    {
        //
        //
        $request->validate([
            'nik' => 'required|unique:penduduk|max:16',
            'nama' => 'required|string|max:255',
            'lahir' => 'required|string|max:255',
            'tanggal' => 'required|date',
            'kelamin' => 'required|string|max:255',
            'agama' => 'required|string|max:255',
            'kawin' => 'nullable|string|max:255',
            'pendidikan' => 'required|string|max:255',
            'pekerjaan' => 'required|string|max:255',
            'darah' => 'required|string|max:255',
            'kewarganegaran' => 'required|string|max:255',
            'hubungan' => 'nullable|string|max:255',
            'keluarga_id' => 'required|exists:keluargas,id',
            'telepon' => 'string|max:255',
            'alamat' => 'required|string|max:255',
        ]);

        $penduduk->update($request->all());
        return redirect()->back()->with('message', 'Data berhasil diperbarui!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        DB::table('penduduks')
            ->where('id', $id)
            ->update(['deleted_at' => now()]);
    
        return redirect()->route('penduduk')->with('success', 'Penduduk berhasil dihapus.');
    }

    public function showTrashed(){
        $keluargas = Keluarga::all();
        $penduduks = Penduduk::whereNotNull('deleted_at')->get();
        $penduduks = $penduduks->map(function ($item) {
            // Menghitung usia berdasarkan tahun lahir dan tahun sekarang
            $lahirTahun = Carbon::parse($item->tanggal)->year; // Mengambil tahun lahir
            $sekarangTahun = Carbon::now()->year; // Mengambil tahun sekarang
            $item->usia = $sekarangTahun - $lahirTahun; // Menghitung usia
            return $item;
        });
        return Inertia::render('Trash/PendudukTrash', [
            'penduduks' => $penduduks,
            'keluargas' => $keluargas
        ]);
    }

    public function destroyTrashed($id)
    {
        DB::table('penduduks')->where('id', $id)->delete();

        return redirect()->route('trash.penduduk')->with('success', 'Penduduk berhasil dihapus.');
    }

    public function restoreTrashed($id)
    {
        // Update kolom `deleted_at` menjadi NULL
        DB::table('penduduks')
            ->where('id', $id)
            ->update(['deleted_at' => null]);

        return redirect('/trash-penduduk')->with('success', 'Penduduk has been restored');
    }
}
