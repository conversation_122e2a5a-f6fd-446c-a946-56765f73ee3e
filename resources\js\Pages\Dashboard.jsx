import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout';
import { Head } from '@inertiajs/react';
import Card from '@/Components/Dashboard/Card';
import { usePage } from '@inertiajs/react';
import Chart from '@/Components/Dashboard/Charts';

export default function Dashboard() {
    const {jumlahPenduduk} = usePage().props
    const {lakiLaki} = usePage().props
    const {perempuan} = usePage().props
    const {pekerjaan} = usePage().props
    const {usiaData} = usePage().props
    const {jumlahKeluarga} = usePage().props
    const {rataRataUsia} = usePage().props
    return (
        <AuthenticatedLayout
            header={
                <h2 className="text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200">
                    Dashboard
                </h2>
            }
        >
            <Head title="Dashboard" />

            <div className="py-12">
                <div className="mx-auto max-w-7xl sm:px-6 lg:px-8">
                    <div className="overflow-hidden bg-gray-100 shadow-sm sm:rounded-lg dark:bg-gray-900">
                        <Card jumlahPenduduk={jumlahPenduduk} jumlahKeluarga={jumlahKeluarga} pekerjaan={pekerjaan} rataRataUsia={rataRataUsia}/>
                        <Chart lakiLaki={lakiLaki} perempuan={perempuan} usiaData={usiaData} />
                    </div>
                </div>
            </div>
        </AuthenticatedLayout>
    );
}
