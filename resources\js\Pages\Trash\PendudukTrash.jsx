import React, { useState, useEffect } from 'react'
import { Link, usePage,router } from '@inertiajs/react'
import DataTable from 'react-data-table-component'
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout'
import { MdDelete } from "react-icons/md";
import { IoReader } from "react-icons/io5";
import Modal from 'react-modal';
import DeleteConfirmationModal from '@/Components/Dashboard/DeleteModal';
import toast from 'react-hot-toast';

const PendudukTrash = ({penduduks,keluargas}) => {    
    console.log("penduduk",penduduks)
    console.log("keluarga",keluargas)
    const [searchText, setSearchText] = useState("");
    const [isOpen, setIsOpen] = useState(false);
    const [selectedData, setSelectedData] = useState(null);
    const {props} = usePage();
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [deleteId, setDeleteId] = useState(null);
    const [isModalOpen2, setIsModalOpen2] = useState(false);
    const[editId, setEditId] = useState(null);
    useEffect(() => {
        if (props.message) {
          toast.success(props.message);
        }
        console.log(props.message);
      }, [props.message]);
    console.log("ini penduduk", penduduks);
    // Fungsi untuk membuka modal dengan data yang diklik
    const handleRowClick = (row) => {
        setSelectedData(row); // Simpan data item terpilih
        setIsOpen(true); // Buka modal
    };

    // Fungsi untuk menutup modal
    const closeModal = () => {
        setIsOpen(false);
        setSelectedData(null); // Reset data item terpilih
    };
    const columns = [
        {
            name: 'NIK',
            selector: row => row.nik,
            cell: row => (
                <span 
                    className="text-blue-500 cursor-pointer truncate" 
                    onClick={() => handleRowClick(row)}
                >
                    {row.nik}
                </span>
            ), 
            sortable: true
        },
        {
            name: 'Nama',
            selector: row => row.nama,
            sortable: true
        },
        {
            name: 'Jenis Kelamin',
            selector: row => row.kelamin,
            sortable: true
        },
        {
            name: 'Usia',
            selector: row => row.usia,
            sortable: true
        },
        {
            name: 'Agama',
            selector: row => row.agama,
            sortable: true
        },
        {
            name: 'Pekerjaan',
            selector: row => row.pekerjaan,
            sortable: true
        },
        {
            name: 'Operations',
            cell: row => (
                <div className="flex space-x-2">
                    <button
                        onClick={() => handleEdit(row.id)}
                        className="px-2 py-1 text-white bg-blue-500 rounded hover:bg-blue-700"
                    >
                        <IoReader />
                    </button>
                    <DeleteConfirmationModal
                        isOpen={isModalOpen2}
                        onRequestClose={() => setIsModalOpen2(false)}
                        onConfirm={handleConfirmEdit}
                        id={editId}
                        message="Apakah Anda yakin ingin restore data ini ?"
                    />
                    <button
                        onClick={() => handleDelete(row.id)}
                        className="px-2 py-1 text-white bg-red-500 rounded hover:bg-red-700"
                    >
                        <MdDelete/>
                    </button>
                    <DeleteConfirmationModal
                        isOpen={isModalOpen}
                        onRequestClose={() => setIsModalOpen(false)}
                        onConfirm={handleConfirmDelete}
                        id={deleteId}
                    />
                </div>
            ),
        },
    ];
    
    const handleEdit = (id) => {
        setEditId(id);
        setIsModalOpen2(true);
    };

    const handleConfirmEdit = (id) => {
        const penduduk = penduduks.find(penduduk => penduduk.id === id);
        const keluarga = keluargas.find(keluarga => keluarga.id === penduduk.keluarga_id);
    
        // Cek apakah ditemukan keluarga yang sesuai dengan keluarga_id pada penduduk
        if (keluarga) {
            // Cek apakah deleted_at pada keluarga null atau tidak
            if (keluarga.deleted_at === null) {
                const url = `/trash-penduduk/${id}/restore`;
    
                // Menampilkan toast pertama jika keluarga belum dihapus
                toast.promise(
                    new Promise((resolve, reject) => {
                        router.get(url, {}, {
                            onSuccess: () => {
                                resolve();
                            },
                            onError: (error) => {
                                reject(error);
                            }
                        });
                    }),
                    {
                        success: 'Data berhasil di restore!',
                        error: (error) => `Terjadi kesalahan: ${error}`,
                    }
                );
            } else {
                // Menampilkan toast jika keluarga sudah dihapus
                toast.error('Nomor keluarga tidak terdaftar karena sudah dihapus.');
            }
        } else {
            // Menampilkan toast jika keluarga tidak ditemukan
            toast.error('Keluarga tidak ditemukan.');
        }
    };
    
    const handleDelete = (id) => {
        setDeleteId(id);
        setIsModalOpen(true); // Menampilkan modal konfirmasi
    };

    const handleConfirmDelete = (id) => {
        // Menampilkan toast pertama
        toast.promise(
            new Promise((resolve, reject) => {
                router.delete(`/trash-penduduk/${id}/force`, {
                    onSuccess: () => {
                        resolve(); // Sukses, lanjut ke toast kedua
                    },
                    onError: (error) => {
                        reject(error); // Gagal, tampilkan error
                    }
                });
            }),
            {
                loading: 'Menghapus data...',
                success: 'Data berhasil dihapus!',
                error: (error) => `Terjadi kesalahan: ${error}`,
            }
        );
    };

        const paginationComponentOptions = {
            selectAllRowsItem: true,
            selectAllRowsItemText: "ALL"
        };

        // Filter data berdasarkan pencarian
        const filteredData = penduduks.filter(item =>
            item.nik.toLowerCase().includes(searchText.toLowerCase()) ||
            item.nama.toLowerCase().includes(searchText.toLowerCase()) ||
            item.alamat.toLowerCase().includes(searchText.toLowerCase()) ||
            item.kelamin.toLowerCase().includes(searchText.toLowerCase())||
            item.pekerjaan.toLowerCase().includes(searchText.toLowerCase())||
            item.agama.toLowerCase().includes(searchText.toLowerCase())||
            item.tanggal.toLowerCase().includes(searchText.toLowerCase())||
            item.lahir.toLowerCase().includes(searchText.toLowerCase())
        );
  return (
    <AuthenticatedLayout
    header={
        <h2 className="text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200">
            Trash Penduduk
        </h2>
    }
    >
        <div className="py-0 lg:py-12">
                <div className="mx-auto max-w-4xl sm:px-6 lg:px-8">
                    <div className="overflow-hidden bg-white shadow-sm sm:rounded-lg dark:bg-gray-800">
                        <div className="p-6 text-gray-900 dark:text-gray-100">
                            <div className='flex justify-between gap-2'>
                                <div className=' text-white py-2 px-4 rounded  h-10 text-sm lg:text-xl font-semibold'>
                                    Data Penduduk Trash
                                </div>
                                 {/* Input search */}
                                <input
                                    type="text"
                                    placeholder="Search..."
                                    value={searchText}
                                    onChange={(e) => setSearchText(e.target.value)}
                                    className="mb-4 p-2 border rounded bg-white text-gray-900"
                                />

                            </div>
                            
                            <DataTable
                                columns={columns}
                                data={filteredData}
                                pagination
                                paginationComponentOptions={paginationComponentOptions}
                                paginationTotalRows={penduduks.length}
                                responsive
                            />
                            <Modal
                                isOpen={isOpen}
                                onRequestClose={closeModal}
                                contentLabel="Detail Data"
                                className="bg-white dark:bg-gray-900 rounded-lg p-8 shadow-lg max-w-3xl mx-auto mt-20" // Gunakan kelas max-w-3xl untuk memperbesar lebar modal
                                overlayClassName="bg-black bg-opacity-50 fixed inset-0 flex items-center justify-center"
                            >
                                {selectedData && (
                                    <Detail selectedData={selectedData} closeModal={closeModal} />
                                )}
                            </Modal>
                        </div>
                    </div>
                </div>
            </div>
    </AuthenticatedLayout>
  )
}

export default PendudukTrash