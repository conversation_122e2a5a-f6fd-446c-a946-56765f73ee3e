[2024-12-17 03:26:54] local.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:83)
[stacktrace]
#0 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(393): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}('')
#1 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(81): tap('', Object(Closure))
#2 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(937): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(819): Illuminate\\Container\\Container->build(Object(Closure))
#6 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1048): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(755): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1030): Illuminate\\Container\\Container->make('encrypter', Array)
#9 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1118): Illuminate\\Foundation\\Application->make('encrypter')
#10 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1028): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#11 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(979): Illuminate\\Container\\Container->resolveDependencies(Array)
#12 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(819): Illuminate\\Container\\Container->build('Illuminate\\\\Cook...')
#13 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1048): Illuminate\\Container\\Container->resolve('Illuminate\\\\Cook...', Array, true)
#14 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(755): Illuminate\\Foundation\\Application->resolve('Illuminate\\\\Cook...', Array)
#15 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1030): Illuminate\\Container\\Container->make('Illuminate\\\\Cook...', Array)
#16 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(172): Illuminate\\Foundation\\Application->make('Illuminate\\\\Cook...')
#17 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#43 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1190): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#44 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#45 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('D:\\\\SEMESTER 5\\\\D...')
#46 {main}
"} 
[2024-12-17 03:26:56] local.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:83)
[stacktrace]
#0 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(393): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}('')
#1 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(81): tap('', Object(Closure))
#2 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(937): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(819): Illuminate\\Container\\Container->build(Object(Closure))
#6 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1048): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(755): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1030): Illuminate\\Container\\Container->make('encrypter', Array)
#9 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1118): Illuminate\\Foundation\\Application->make('encrypter')
#10 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1028): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#11 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(979): Illuminate\\Container\\Container->resolveDependencies(Array)
#12 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(819): Illuminate\\Container\\Container->build('Illuminate\\\\Cook...')
#13 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1048): Illuminate\\Container\\Container->resolve('Illuminate\\\\Cook...', Array, true)
#14 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(755): Illuminate\\Foundation\\Application->resolve('Illuminate\\\\Cook...', Array)
#15 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1030): Illuminate\\Container\\Container->make('Illuminate\\\\Cook...', Array)
#16 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(258): Illuminate\\Foundation\\Application->make('Illuminate\\\\Cook...')
#17 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(216): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#18 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1192): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#19 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#20 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('D:\\\\SEMESTER 5\\\\D...')
#21 {main}
"} 
[2025-06-15 13:46:37] local.ERROR: Command "server" is not defined.

Did you mean one of these?
    make:observer
    serve {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"server\" is not defined.

Did you mean one of these?
    make:observer
    serve at D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php:726)
[stacktrace]
#0 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find()
#1 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#2 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run()
#3 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1205): Illuminate\\Foundation\\Console\\Kernel->handle()
#4 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\artisan(13): Illuminate\\Foundation\\Application->handleCommand()
#5 {main}
"} 
