[2024-12-17 03:26:54] local.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:83)
[stacktrace]
#0 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(393): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}('')
#1 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(81): tap('', Object(Closure))
#2 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(937): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(819): Illuminate\\Container\\Container->build(Object(Closure))
#6 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1048): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(755): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1030): Illuminate\\Container\\Container->make('encrypter', Array)
#9 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1118): Illuminate\\Foundation\\Application->make('encrypter')
#10 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1028): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#11 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(979): Illuminate\\Container\\Container->resolveDependencies(Array)
#12 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(819): Illuminate\\Container\\Container->build('Illuminate\\\\Cook...')
#13 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1048): Illuminate\\Container\\Container->resolve('Illuminate\\\\Cook...', Array, true)
#14 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(755): Illuminate\\Foundation\\Application->resolve('Illuminate\\\\Cook...', Array)
#15 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1030): Illuminate\\Container\\Container->make('Illuminate\\\\Cook...', Array)
#16 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(172): Illuminate\\Foundation\\Application->make('Illuminate\\\\Cook...')
#17 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#43 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1190): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#44 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#45 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('D:\\\\SEMESTER 5\\\\D...')
#46 {main}
"} 
[2024-12-17 03:26:56] local.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:83)
[stacktrace]
#0 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(393): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}('')
#1 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(81): tap('', Object(Closure))
#2 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(937): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(819): Illuminate\\Container\\Container->build(Object(Closure))
#6 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1048): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(755): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1030): Illuminate\\Container\\Container->make('encrypter', Array)
#9 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1118): Illuminate\\Foundation\\Application->make('encrypter')
#10 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1028): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#11 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(979): Illuminate\\Container\\Container->resolveDependencies(Array)
#12 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(819): Illuminate\\Container\\Container->build('Illuminate\\\\Cook...')
#13 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1048): Illuminate\\Container\\Container->resolve('Illuminate\\\\Cook...', Array, true)
#14 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(755): Illuminate\\Foundation\\Application->resolve('Illuminate\\\\Cook...', Array)
#15 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1030): Illuminate\\Container\\Container->make('Illuminate\\\\Cook...', Array)
#16 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(258): Illuminate\\Foundation\\Application->make('Illuminate\\\\Cook...')
#17 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(216): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#18 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1192): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#19 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#20 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('D:\\\\SEMESTER 5\\\\D...')
#21 {main}
"} 
[2025-06-15 13:46:37] local.ERROR: Command "server" is not defined.

Did you mean one of these?
    make:observer
    serve {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"server\" is not defined.

Did you mean one of these?
    make:observer
    serve at D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php:726)
[stacktrace]
#0 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find()
#1 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#2 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run()
#3 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1205): Illuminate\\Foundation\\Console\\Kernel->handle()
#4 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\artisan(13): Illuminate\\Foundation\\Application->handleCommand()
#5 {main}
"} 
[2025-06-15 13:48:59] local.ERROR: The [pcntl] extension is required to run Pail. {"exception":"[object] (RuntimeException(code: 0): The [pcntl] extension is required to run Pail. at D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\pail\\src\\Guards\\EnsurePcntlIsAvailable.php:15)
[stacktrace]
#0 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\pail\\src\\Console\\Commands\\PailCommand.php(46): Laravel\\Pail\\Guards\\EnsurePcntlIsAvailable::check()
#1 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Pail\\Console\\Commands\\PailCommand->handle()
#2 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#3 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure()
#4 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#5 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call()
#6 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call()
#7 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#8 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()
#9 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#10 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#11 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#12 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run()
#13 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1205): Illuminate\\Foundation\\Console\\Kernel->handle()
#14 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\artisan(13): Illuminate\\Foundation\\Application->handleCommand()
#15 {main}
"} 
[2025-06-15 13:49:00] local.ERROR: could not find driver {"exception":"[object] (PDOException(code: 0): could not find driver at D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:67)
[stacktrace]
#0 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(67): PDO::connect()
#1 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#3 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->{closure:Illuminate\\Database\\Connectors\\ConnectionFactory::createPdoResolverWithHosts():180}()
#5 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func()
#6 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(151): Illuminate\\Database\\Connection->getPdo()
#7 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(127): Illuminate\\Database\\Connection->createTransaction()
#8 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(26): Illuminate\\Database\\Connection->beginTransaction()
#9 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction()
#10 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(351): Illuminate\\Queue\\DatabaseQueue->pop()
#11 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->{closure:Illuminate\\Queue\\Worker::getNextJob():350}()
#12 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(327): Illuminate\\Queue\\Worker->getNextJob()
#13 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob()
#14 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker()
#15 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#16 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#17 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure()
#18 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#19 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call()
#20 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call()
#21 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#22 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()
#23 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#24 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#25 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#26 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run()
#27 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1205): Illuminate\\Foundation\\Console\\Kernel->handle()
#28 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\artisan(13): Illuminate\\Foundation\\Application->handleCommand()
#29 {main}
"} 
[2025-06-15 13:49:04] local.ERROR: could not find driver {"exception":"[object] (PDOException(code: 0): could not find driver at D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:67)
[stacktrace]
#0 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(67): PDO::connect()
#1 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#3 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->{closure:Illuminate\\Database\\Connectors\\ConnectionFactory::createPdoResolverWithHosts():180}()
#5 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func()
#6 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(151): Illuminate\\Database\\Connection->getPdo()
#7 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(127): Illuminate\\Database\\Connection->createTransaction()
#8 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(26): Illuminate\\Database\\Connection->beginTransaction()
#9 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction()
#10 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(351): Illuminate\\Queue\\DatabaseQueue->pop()
#11 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->{closure:Illuminate\\Queue\\Worker::getNextJob():350}()
#12 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(327): Illuminate\\Queue\\Worker->getNextJob()
#13 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob()
#14 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker()
#15 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#16 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#17 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure()
#18 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#19 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call()
#20 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call()
#21 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#22 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()
#23 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#24 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#25 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#26 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run()
#27 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1205): Illuminate\\Foundation\\Console\\Kernel->handle()
#28 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\artisan(13): Illuminate\\Foundation\\Application->handleCommand()
#29 {main}
"} 
[2025-06-15 13:49:09] local.ERROR: could not find driver {"exception":"[object] (PDOException(code: 0): could not find driver at D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:67)
[stacktrace]
#0 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(67): PDO::connect()
#1 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#3 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->{closure:Illuminate\\Database\\Connectors\\ConnectionFactory::createPdoResolverWithHosts():180}()
#5 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func()
#6 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(151): Illuminate\\Database\\Connection->getPdo()
#7 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(127): Illuminate\\Database\\Connection->createTransaction()
#8 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(26): Illuminate\\Database\\Connection->beginTransaction()
#9 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction()
#10 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(351): Illuminate\\Queue\\DatabaseQueue->pop()
#11 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->{closure:Illuminate\\Queue\\Worker::getNextJob():350}()
#12 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(327): Illuminate\\Queue\\Worker->getNextJob()
#13 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob()
#14 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker()
#15 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#16 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#17 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure()
#18 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#19 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call()
#20 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call()
#21 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#22 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()
#23 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#24 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#25 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#26 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run()
#27 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1205): Illuminate\\Foundation\\Console\\Kernel->handle()
#28 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\artisan(13): Illuminate\\Foundation\\Application->handleCommand()
#29 {main}
"} 
[2025-06-15 13:49:13] local.ERROR: could not find driver {"exception":"[object] (PDOException(code: 0): could not find driver at D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:67)
[stacktrace]
#0 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(67): PDO::connect()
#1 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#3 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->{closure:Illuminate\\Database\\Connectors\\ConnectionFactory::createPdoResolverWithHosts():180}()
#5 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func()
#6 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(151): Illuminate\\Database\\Connection->getPdo()
#7 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(127): Illuminate\\Database\\Connection->createTransaction()
#8 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(26): Illuminate\\Database\\Connection->beginTransaction()
#9 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction()
#10 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(351): Illuminate\\Queue\\DatabaseQueue->pop()
#11 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->{closure:Illuminate\\Queue\\Worker::getNextJob():350}()
#12 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(327): Illuminate\\Queue\\Worker->getNextJob()
#13 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob()
#14 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker()
#15 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#16 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#17 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure()
#18 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#19 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call()
#20 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call()
#21 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#22 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()
#23 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#24 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#25 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#26 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run()
#27 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1205): Illuminate\\Foundation\\Console\\Kernel->handle()
#28 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\artisan(13): Illuminate\\Foundation\\Application->handleCommand()
#29 {main}
"} 
[2025-06-15 13:49:18] local.ERROR: could not find driver {"exception":"[object] (PDOException(code: 0): could not find driver at D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:67)
[stacktrace]
#0 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(67): PDO::connect()
#1 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#3 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->{closure:Illuminate\\Database\\Connectors\\ConnectionFactory::createPdoResolverWithHosts():180}()
#5 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func()
#6 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(151): Illuminate\\Database\\Connection->getPdo()
#7 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(127): Illuminate\\Database\\Connection->createTransaction()
#8 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(26): Illuminate\\Database\\Connection->beginTransaction()
#9 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction()
#10 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(351): Illuminate\\Queue\\DatabaseQueue->pop()
#11 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->{closure:Illuminate\\Queue\\Worker::getNextJob():350}()
#12 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(327): Illuminate\\Queue\\Worker->getNextJob()
#13 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob()
#14 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker()
#15 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#16 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#17 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure()
#18 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#19 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call()
#20 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call()
#21 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#22 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()
#23 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#24 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#25 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#26 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run()
#27 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1205): Illuminate\\Foundation\\Console\\Kernel->handle()
#28 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\artisan(13): Illuminate\\Foundation\\Application->handleCommand()
#29 {main}
"} 
[2025-06-15 13:49:23] local.ERROR: could not find driver {"exception":"[object] (PDOException(code: 0): could not find driver at D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:67)
[stacktrace]
#0 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(67): PDO::connect()
#1 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#3 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->{closure:Illuminate\\Database\\Connectors\\ConnectionFactory::createPdoResolverWithHosts():180}()
#5 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func()
#6 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(151): Illuminate\\Database\\Connection->getPdo()
#7 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(127): Illuminate\\Database\\Connection->createTransaction()
#8 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(26): Illuminate\\Database\\Connection->beginTransaction()
#9 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction()
#10 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(351): Illuminate\\Queue\\DatabaseQueue->pop()
#11 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->{closure:Illuminate\\Queue\\Worker::getNextJob():350}()
#12 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(327): Illuminate\\Queue\\Worker->getNextJob()
#13 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob()
#14 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker()
#15 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#16 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#17 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure()
#18 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#19 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call()
#20 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call()
#21 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#22 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()
#23 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#24 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#25 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#26 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run()
#27 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1205): Illuminate\\Foundation\\Console\\Kernel->handle()
#28 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\artisan(13): Illuminate\\Foundation\\Application->handleCommand()
#29 {main}
"} 
[2025-06-15 13:49:27] local.ERROR: could not find driver {"exception":"[object] (PDOException(code: 0): could not find driver at D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:67)
[stacktrace]
#0 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(67): PDO::connect()
#1 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#3 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->{closure:Illuminate\\Database\\Connectors\\ConnectionFactory::createPdoResolverWithHosts():180}()
#5 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func()
#6 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(151): Illuminate\\Database\\Connection->getPdo()
#7 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(127): Illuminate\\Database\\Connection->createTransaction()
#8 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(26): Illuminate\\Database\\Connection->beginTransaction()
#9 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction()
#10 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(351): Illuminate\\Queue\\DatabaseQueue->pop()
#11 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->{closure:Illuminate\\Queue\\Worker::getNextJob():350}()
#12 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(327): Illuminate\\Queue\\Worker->getNextJob()
#13 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob()
#14 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker()
#15 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#16 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#17 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure()
#18 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#19 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call()
#20 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call()
#21 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#22 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()
#23 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#24 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#25 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#26 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run()
#27 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1205): Illuminate\\Foundation\\Console\\Kernel->handle()
#28 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\artisan(13): Illuminate\\Foundation\\Application->handleCommand()
#29 {main}
"} 
[2025-06-15 13:49:31] local.ERROR: could not find driver (Connection: mysql, SQL: select * from `sessions` where `id` = QJxgWme25Axjxd5NEcOLmCWq3exBL6KLoRJq2aWw limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): could not find driver (Connection: mysql, SQL: select * from `sessions` where `id` = QJxgWme25Axjxd5NEcOLmCWq3exBL6KLoRJq2aWw limit 1) at D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback()
#1 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run()
#2 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3133): Illuminate\\Database\\Connection->select()
#3 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3118): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3706): Illuminate\\Database\\Query\\Builder->{closure:Illuminate\\Database\\Query\\Builder::get():3117}()
#5 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3117): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#6 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(344): Illuminate\\Database\\Query\\Builder->get()
#7 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3038): Illuminate\\Database\\Query\\Builder->first()
#8 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php(97): Illuminate\\Database\\Query\\Builder->find()
#9 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(114): Illuminate\\Session\\DatabaseSessionHandler->read()
#10 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(102): Illuminate\\Session\\Store->readFromHandler()
#11 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(86): Illuminate\\Session\\Store->loadSession()
#12 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(147): Illuminate\\Session\\Store->start()
#13 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(393): Illuminate\\Session\\Middleware\\StartSession->{closure:Illuminate\\Session\\Middleware\\StartSession::startSession():144}()
#14 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(144): tap()
#15 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(116): Illuminate\\Session\\Middleware\\StartSession->startSession()
#16 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#17 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#18 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#19 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#20 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#21 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#22 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#23 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#24 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#25 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#26 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#27 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch()
#28 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}()
#29 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}()
#30 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#31 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#32 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#33 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#34 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#35 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#36 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#37 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#38 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#39 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#40 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#41 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#42 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#43 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#44 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#45 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#46 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then()
#47 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#48 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1190): Illuminate\\Foundation\\Http\\Kernel->handle()
#49 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest()
#50 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#51 {main}

[previous exception] [object] (PDOException(code: 0): could not find driver at D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:67)
[stacktrace]
#0 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(67): PDO::connect()
#1 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#3 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->{closure:Illuminate\\Database\\Connectors\\ConnectionFactory::createPdoResolverWithHosts():180}()
#5 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func()
#6 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1267): Illuminate\\Database\\Connection->getPdo()
#7 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(512): Illuminate\\Database\\Connection->getReadPdo()
#8 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(407): Illuminate\\Database\\Connection->getPdoForSelect()
#9 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::select():398}()
#10 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback()
#11 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run()
#12 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3133): Illuminate\\Database\\Connection->select()
#13 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3118): Illuminate\\Database\\Query\\Builder->runSelect()
#14 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3706): Illuminate\\Database\\Query\\Builder->{closure:Illuminate\\Database\\Query\\Builder::get():3117}()
#15 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3117): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#16 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(344): Illuminate\\Database\\Query\\Builder->get()
#17 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3038): Illuminate\\Database\\Query\\Builder->first()
#18 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php(97): Illuminate\\Database\\Query\\Builder->find()
#19 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(114): Illuminate\\Session\\DatabaseSessionHandler->read()
#20 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(102): Illuminate\\Session\\Store->readFromHandler()
#21 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(86): Illuminate\\Session\\Store->loadSession()
#22 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(147): Illuminate\\Session\\Store->start()
#23 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(393): Illuminate\\Session\\Middleware\\StartSession->{closure:Illuminate\\Session\\Middleware\\StartSession::startSession():144}()
#24 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(144): tap()
#25 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(116): Illuminate\\Session\\Middleware\\StartSession->startSession()
#26 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#27 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#28 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#29 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#30 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#31 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#32 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#33 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#34 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#35 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#36 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#37 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch()
#38 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}()
#39 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}()
#40 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#41 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#42 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#43 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#44 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#45 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#46 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#47 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#48 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#49 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#50 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#51 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#52 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#53 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#54 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#55 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#56 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then()
#57 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#58 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1190): Illuminate\\Foundation\\Http\\Kernel->handle()
#59 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest()
#60 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#61 {main}
"} 
[2025-06-15 13:49:32] local.ERROR: could not find driver {"exception":"[object] (PDOException(code: 0): could not find driver at D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:67)
[stacktrace]
#0 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(67): PDO::connect()
#1 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#3 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->{closure:Illuminate\\Database\\Connectors\\ConnectionFactory::createPdoResolverWithHosts():180}()
#5 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func()
#6 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(151): Illuminate\\Database\\Connection->getPdo()
#7 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(127): Illuminate\\Database\\Connection->createTransaction()
#8 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(26): Illuminate\\Database\\Connection->beginTransaction()
#9 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction()
#10 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(351): Illuminate\\Queue\\DatabaseQueue->pop()
#11 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->{closure:Illuminate\\Queue\\Worker::getNextJob():350}()
#12 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(327): Illuminate\\Queue\\Worker->getNextJob()
#13 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob()
#14 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker()
#15 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#16 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#17 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure()
#18 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#19 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call()
#20 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call()
#21 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#22 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()
#23 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#24 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#25 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#26 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run()
#27 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1205): Illuminate\\Foundation\\Console\\Kernel->handle()
#28 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\artisan(13): Illuminate\\Foundation\\Application->handleCommand()
#29 {main}
"} 
[2025-06-15 13:49:36] local.ERROR: could not find driver {"exception":"[object] (PDOException(code: 0): could not find driver at D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:67)
[stacktrace]
#0 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(67): PDO::connect()
#1 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#3 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->{closure:Illuminate\\Database\\Connectors\\ConnectionFactory::createPdoResolverWithHosts():180}()
#5 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func()
#6 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(151): Illuminate\\Database\\Connection->getPdo()
#7 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(127): Illuminate\\Database\\Connection->createTransaction()
#8 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(26): Illuminate\\Database\\Connection->beginTransaction()
#9 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction()
#10 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(351): Illuminate\\Queue\\DatabaseQueue->pop()
#11 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->{closure:Illuminate\\Queue\\Worker::getNextJob():350}()
#12 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(327): Illuminate\\Queue\\Worker->getNextJob()
#13 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob()
#14 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker()
#15 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#16 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#17 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure()
#18 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#19 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call()
#20 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call()
#21 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#22 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()
#23 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#24 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#25 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#26 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run()
#27 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1205): Illuminate\\Foundation\\Console\\Kernel->handle()
#28 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\artisan(13): Illuminate\\Foundation\\Application->handleCommand()
#29 {main}
"} 
[2025-06-15 13:49:41] local.ERROR: could not find driver {"exception":"[object] (PDOException(code: 0): could not find driver at D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:67)
[stacktrace]
#0 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(67): PDO::connect()
#1 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#3 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->{closure:Illuminate\\Database\\Connectors\\ConnectionFactory::createPdoResolverWithHosts():180}()
#5 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func()
#6 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(151): Illuminate\\Database\\Connection->getPdo()
#7 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(127): Illuminate\\Database\\Connection->createTransaction()
#8 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(26): Illuminate\\Database\\Connection->beginTransaction()
#9 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction()
#10 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(351): Illuminate\\Queue\\DatabaseQueue->pop()
#11 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->{closure:Illuminate\\Queue\\Worker::getNextJob():350}()
#12 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(327): Illuminate\\Queue\\Worker->getNextJob()
#13 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob()
#14 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker()
#15 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#16 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#17 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure()
#18 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#19 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call()
#20 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call()
#21 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#22 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()
#23 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#24 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#25 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#26 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run()
#27 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1205): Illuminate\\Foundation\\Console\\Kernel->handle()
#28 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\artisan(13): Illuminate\\Foundation\\Application->handleCommand()
#29 {main}
"} 
[2025-06-15 13:49:45] local.ERROR: could not find driver {"exception":"[object] (PDOException(code: 0): could not find driver at D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:67)
[stacktrace]
#0 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(67): PDO::connect()
#1 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#3 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->{closure:Illuminate\\Database\\Connectors\\ConnectionFactory::createPdoResolverWithHosts():180}()
#5 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func()
#6 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(151): Illuminate\\Database\\Connection->getPdo()
#7 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(127): Illuminate\\Database\\Connection->createTransaction()
#8 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(26): Illuminate\\Database\\Connection->beginTransaction()
#9 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction()
#10 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(351): Illuminate\\Queue\\DatabaseQueue->pop()
#11 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->{closure:Illuminate\\Queue\\Worker::getNextJob():350}()
#12 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(327): Illuminate\\Queue\\Worker->getNextJob()
#13 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob()
#14 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker()
#15 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#16 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#17 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure()
#18 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#19 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call()
#20 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call()
#21 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#22 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()
#23 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#24 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#25 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#26 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run()
#27 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1205): Illuminate\\Foundation\\Console\\Kernel->handle()
#28 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\artisan(13): Illuminate\\Foundation\\Application->handleCommand()
#29 {main}
"} 
[2025-06-15 13:49:50] local.ERROR: could not find driver {"exception":"[object] (PDOException(code: 0): could not find driver at D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:67)
[stacktrace]
#0 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(67): PDO::connect()
#1 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#3 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->{closure:Illuminate\\Database\\Connectors\\ConnectionFactory::createPdoResolverWithHosts():180}()
#5 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func()
#6 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(151): Illuminate\\Database\\Connection->getPdo()
#7 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(127): Illuminate\\Database\\Connection->createTransaction()
#8 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(26): Illuminate\\Database\\Connection->beginTransaction()
#9 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction()
#10 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(351): Illuminate\\Queue\\DatabaseQueue->pop()
#11 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->{closure:Illuminate\\Queue\\Worker::getNextJob():350}()
#12 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(327): Illuminate\\Queue\\Worker->getNextJob()
#13 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob()
#14 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker()
#15 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#16 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#17 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure()
#18 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#19 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call()
#20 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call()
#21 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#22 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()
#23 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#24 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#25 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#26 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run()
#27 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1205): Illuminate\\Foundation\\Console\\Kernel->handle()
#28 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\artisan(13): Illuminate\\Foundation\\Application->handleCommand()
#29 {main}
"} 
[2025-06-15 13:49:54] local.ERROR: could not find driver {"exception":"[object] (PDOException(code: 0): could not find driver at D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:67)
[stacktrace]
#0 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(67): PDO::connect()
#1 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#3 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->{closure:Illuminate\\Database\\Connectors\\ConnectionFactory::createPdoResolverWithHosts():180}()
#5 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func()
#6 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(151): Illuminate\\Database\\Connection->getPdo()
#7 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(127): Illuminate\\Database\\Connection->createTransaction()
#8 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(26): Illuminate\\Database\\Connection->beginTransaction()
#9 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction()
#10 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(351): Illuminate\\Queue\\DatabaseQueue->pop()
#11 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->{closure:Illuminate\\Queue\\Worker::getNextJob():350}()
#12 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(327): Illuminate\\Queue\\Worker->getNextJob()
#13 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob()
#14 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker()
#15 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#16 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#17 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure()
#18 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#19 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call()
#20 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call()
#21 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#22 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()
#23 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#24 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#25 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#26 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run()
#27 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1205): Illuminate\\Foundation\\Console\\Kernel->handle()
#28 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\artisan(13): Illuminate\\Foundation\\Application->handleCommand()
#29 {main}
"} 
[2025-06-15 13:49:59] local.ERROR: could not find driver {"exception":"[object] (PDOException(code: 0): could not find driver at D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:67)
[stacktrace]
#0 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(67): PDO::connect()
#1 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#3 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->{closure:Illuminate\\Database\\Connectors\\ConnectionFactory::createPdoResolverWithHosts():180}()
#5 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func()
#6 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(151): Illuminate\\Database\\Connection->getPdo()
#7 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(127): Illuminate\\Database\\Connection->createTransaction()
#8 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(26): Illuminate\\Database\\Connection->beginTransaction()
#9 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction()
#10 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(351): Illuminate\\Queue\\DatabaseQueue->pop()
#11 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->{closure:Illuminate\\Queue\\Worker::getNextJob():350}()
#12 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(327): Illuminate\\Queue\\Worker->getNextJob()
#13 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob()
#14 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker()
#15 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#16 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#17 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure()
#18 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#19 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call()
#20 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call()
#21 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#22 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()
#23 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#24 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#25 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#26 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run()
#27 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1205): Illuminate\\Foundation\\Console\\Kernel->handle()
#28 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\artisan(13): Illuminate\\Foundation\\Application->handleCommand()
#29 {main}
"} 
[2025-06-15 13:50:04] local.ERROR: could not find driver {"exception":"[object] (PDOException(code: 0): could not find driver at D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:67)
[stacktrace]
#0 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(67): PDO::connect()
#1 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#3 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->{closure:Illuminate\\Database\\Connectors\\ConnectionFactory::createPdoResolverWithHosts():180}()
#5 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func()
#6 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(151): Illuminate\\Database\\Connection->getPdo()
#7 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(127): Illuminate\\Database\\Connection->createTransaction()
#8 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(26): Illuminate\\Database\\Connection->beginTransaction()
#9 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction()
#10 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(351): Illuminate\\Queue\\DatabaseQueue->pop()
#11 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->{closure:Illuminate\\Queue\\Worker::getNextJob():350}()
#12 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(327): Illuminate\\Queue\\Worker->getNextJob()
#13 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob()
#14 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker()
#15 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#16 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#17 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure()
#18 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#19 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call()
#20 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call()
#21 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#22 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()
#23 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#24 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#25 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#26 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run()
#27 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1205): Illuminate\\Foundation\\Console\\Kernel->handle()
#28 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\artisan(13): Illuminate\\Foundation\\Application->handleCommand()
#29 {main}
"} 
[2025-06-15 13:50:08] local.ERROR: could not find driver {"exception":"[object] (PDOException(code: 0): could not find driver at D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:67)
[stacktrace]
#0 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(67): PDO::connect()
#1 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#3 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->{closure:Illuminate\\Database\\Connectors\\ConnectionFactory::createPdoResolverWithHosts():180}()
#5 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func()
#6 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(151): Illuminate\\Database\\Connection->getPdo()
#7 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(127): Illuminate\\Database\\Connection->createTransaction()
#8 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(26): Illuminate\\Database\\Connection->beginTransaction()
#9 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction()
#10 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(351): Illuminate\\Queue\\DatabaseQueue->pop()
#11 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->{closure:Illuminate\\Queue\\Worker::getNextJob():350}()
#12 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(327): Illuminate\\Queue\\Worker->getNextJob()
#13 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob()
#14 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker()
#15 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#16 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#17 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure()
#18 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#19 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call()
#20 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call()
#21 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#22 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()
#23 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#24 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#25 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#26 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run()
#27 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1205): Illuminate\\Foundation\\Console\\Kernel->handle()
#28 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\artisan(13): Illuminate\\Foundation\\Application->handleCommand()
#29 {main}
"} 
[2025-06-15 13:50:13] local.ERROR: could not find driver {"exception":"[object] (PDOException(code: 0): could not find driver at D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:67)
[stacktrace]
#0 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(67): PDO::connect()
#1 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#3 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->{closure:Illuminate\\Database\\Connectors\\ConnectionFactory::createPdoResolverWithHosts():180}()
#5 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func()
#6 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(151): Illuminate\\Database\\Connection->getPdo()
#7 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(127): Illuminate\\Database\\Connection->createTransaction()
#8 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(26): Illuminate\\Database\\Connection->beginTransaction()
#9 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction()
#10 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(351): Illuminate\\Queue\\DatabaseQueue->pop()
#11 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->{closure:Illuminate\\Queue\\Worker::getNextJob():350}()
#12 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(327): Illuminate\\Queue\\Worker->getNextJob()
#13 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob()
#14 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker()
#15 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#16 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#17 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure()
#18 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#19 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call()
#20 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call()
#21 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#22 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()
#23 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#24 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#25 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#26 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run()
#27 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1205): Illuminate\\Foundation\\Console\\Kernel->handle()
#28 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\artisan(13): Illuminate\\Foundation\\Application->handleCommand()
#29 {main}
"} 
[2025-06-15 13:50:17] local.ERROR: could not find driver {"exception":"[object] (PDOException(code: 0): could not find driver at D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:67)
[stacktrace]
#0 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(67): PDO::connect()
#1 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#3 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->{closure:Illuminate\\Database\\Connectors\\ConnectionFactory::createPdoResolverWithHosts():180}()
#5 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func()
#6 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(151): Illuminate\\Database\\Connection->getPdo()
#7 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(127): Illuminate\\Database\\Connection->createTransaction()
#8 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(26): Illuminate\\Database\\Connection->beginTransaction()
#9 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction()
#10 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(351): Illuminate\\Queue\\DatabaseQueue->pop()
#11 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->{closure:Illuminate\\Queue\\Worker::getNextJob():350}()
#12 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(327): Illuminate\\Queue\\Worker->getNextJob()
#13 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob()
#14 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker()
#15 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#16 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#17 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure()
#18 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#19 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call()
#20 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call()
#21 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#22 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()
#23 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#24 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#25 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#26 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run()
#27 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1205): Illuminate\\Foundation\\Console\\Kernel->handle()
#28 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\artisan(13): Illuminate\\Foundation\\Application->handleCommand()
#29 {main}
"} 
[2025-06-15 13:50:22] local.ERROR: could not find driver {"exception":"[object] (PDOException(code: 0): could not find driver at D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:67)
[stacktrace]
#0 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(67): PDO::connect()
#1 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#3 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->{closure:Illuminate\\Database\\Connectors\\ConnectionFactory::createPdoResolverWithHosts():180}()
#5 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func()
#6 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(151): Illuminate\\Database\\Connection->getPdo()
#7 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(127): Illuminate\\Database\\Connection->createTransaction()
#8 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(26): Illuminate\\Database\\Connection->beginTransaction()
#9 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction()
#10 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(351): Illuminate\\Queue\\DatabaseQueue->pop()
#11 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->{closure:Illuminate\\Queue\\Worker::getNextJob():350}()
#12 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(327): Illuminate\\Queue\\Worker->getNextJob()
#13 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob()
#14 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker()
#15 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#16 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#17 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure()
#18 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#19 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call()
#20 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call()
#21 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#22 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()
#23 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#24 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#25 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#26 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run()
#27 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1205): Illuminate\\Foundation\\Console\\Kernel->handle()
#28 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\artisan(13): Illuminate\\Foundation\\Application->handleCommand()
#29 {main}
"} 
[2025-06-15 13:50:27] local.ERROR: could not find driver {"exception":"[object] (PDOException(code: 0): could not find driver at D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:67)
[stacktrace]
#0 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(67): PDO::connect()
#1 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#3 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->{closure:Illuminate\\Database\\Connectors\\ConnectionFactory::createPdoResolverWithHosts():180}()
#5 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func()
#6 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(151): Illuminate\\Database\\Connection->getPdo()
#7 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(127): Illuminate\\Database\\Connection->createTransaction()
#8 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(26): Illuminate\\Database\\Connection->beginTransaction()
#9 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction()
#10 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(351): Illuminate\\Queue\\DatabaseQueue->pop()
#11 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->{closure:Illuminate\\Queue\\Worker::getNextJob():350}()
#12 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(327): Illuminate\\Queue\\Worker->getNextJob()
#13 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob()
#14 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker()
#15 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#16 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#17 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure()
#18 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#19 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call()
#20 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call()
#21 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#22 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()
#23 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#24 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#25 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#26 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run()
#27 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1205): Illuminate\\Foundation\\Console\\Kernel->handle()
#28 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\artisan(13): Illuminate\\Foundation\\Application->handleCommand()
#29 {main}
"} 
[2025-06-15 13:50:31] local.ERROR: could not find driver {"exception":"[object] (PDOException(code: 0): could not find driver at D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:67)
[stacktrace]
#0 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(67): PDO::connect()
#1 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#3 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->{closure:Illuminate\\Database\\Connectors\\ConnectionFactory::createPdoResolverWithHosts():180}()
#5 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func()
#6 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(151): Illuminate\\Database\\Connection->getPdo()
#7 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(127): Illuminate\\Database\\Connection->createTransaction()
#8 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(26): Illuminate\\Database\\Connection->beginTransaction()
#9 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction()
#10 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(351): Illuminate\\Queue\\DatabaseQueue->pop()
#11 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->{closure:Illuminate\\Queue\\Worker::getNextJob():350}()
#12 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(327): Illuminate\\Queue\\Worker->getNextJob()
#13 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob()
#14 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker()
#15 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#16 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#17 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure()
#18 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#19 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call()
#20 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call()
#21 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#22 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()
#23 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#24 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#25 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#26 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run()
#27 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1205): Illuminate\\Foundation\\Console\\Kernel->handle()
#28 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\artisan(13): Illuminate\\Foundation\\Application->handleCommand()
#29 {main}
"} 
[2025-06-15 13:50:36] local.ERROR: could not find driver {"exception":"[object] (PDOException(code: 0): could not find driver at D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:67)
[stacktrace]
#0 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(67): PDO::connect()
#1 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#3 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->{closure:Illuminate\\Database\\Connectors\\ConnectionFactory::createPdoResolverWithHosts():180}()
#5 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func()
#6 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(151): Illuminate\\Database\\Connection->getPdo()
#7 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(127): Illuminate\\Database\\Connection->createTransaction()
#8 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(26): Illuminate\\Database\\Connection->beginTransaction()
#9 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction()
#10 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(351): Illuminate\\Queue\\DatabaseQueue->pop()
#11 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->{closure:Illuminate\\Queue\\Worker::getNextJob():350}()
#12 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(327): Illuminate\\Queue\\Worker->getNextJob()
#13 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob()
#14 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker()
#15 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#16 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#17 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure()
#18 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#19 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call()
#20 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call()
#21 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#22 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()
#23 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#24 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#25 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#26 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run()
#27 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1205): Illuminate\\Foundation\\Console\\Kernel->handle()
#28 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\artisan(13): Illuminate\\Foundation\\Application->handleCommand()
#29 {main}
"} 
[2025-06-15 13:50:40] local.ERROR: could not find driver {"exception":"[object] (PDOException(code: 0): could not find driver at D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:67)
[stacktrace]
#0 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(67): PDO::connect()
#1 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#3 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->{closure:Illuminate\\Database\\Connectors\\ConnectionFactory::createPdoResolverWithHosts():180}()
#5 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func()
#6 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(151): Illuminate\\Database\\Connection->getPdo()
#7 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(127): Illuminate\\Database\\Connection->createTransaction()
#8 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(26): Illuminate\\Database\\Connection->beginTransaction()
#9 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction()
#10 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(351): Illuminate\\Queue\\DatabaseQueue->pop()
#11 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->{closure:Illuminate\\Queue\\Worker::getNextJob():350}()
#12 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(327): Illuminate\\Queue\\Worker->getNextJob()
#13 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob()
#14 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker()
#15 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#16 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#17 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure()
#18 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#19 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call()
#20 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call()
#21 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#22 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()
#23 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#24 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#25 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#26 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run()
#27 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1205): Illuminate\\Foundation\\Console\\Kernel->handle()
#28 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\artisan(13): Illuminate\\Foundation\\Application->handleCommand()
#29 {main}
"} 
[2025-06-15 13:50:45] local.ERROR: could not find driver {"exception":"[object] (PDOException(code: 0): could not find driver at D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:67)
[stacktrace]
#0 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(67): PDO::connect()
#1 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#3 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->{closure:Illuminate\\Database\\Connectors\\ConnectionFactory::createPdoResolverWithHosts():180}()
#5 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func()
#6 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(151): Illuminate\\Database\\Connection->getPdo()
#7 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(127): Illuminate\\Database\\Connection->createTransaction()
#8 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(26): Illuminate\\Database\\Connection->beginTransaction()
#9 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction()
#10 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(351): Illuminate\\Queue\\DatabaseQueue->pop()
#11 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->{closure:Illuminate\\Queue\\Worker::getNextJob():350}()
#12 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(327): Illuminate\\Queue\\Worker->getNextJob()
#13 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob()
#14 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker()
#15 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#16 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#17 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure()
#18 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#19 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call()
#20 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call()
#21 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#22 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()
#23 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#24 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#25 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#26 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run()
#27 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1205): Illuminate\\Foundation\\Console\\Kernel->handle()
#28 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\artisan(13): Illuminate\\Foundation\\Application->handleCommand()
#29 {main}
"} 
[2025-06-15 13:50:47] local.ERROR: could not find driver (Connection: mysql, SQL: select * from `sessions` where `id` = B5m1C38HeChtbukyY3BUqwsMZ0zdOto0we6tsOnF limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): could not find driver (Connection: mysql, SQL: select * from `sessions` where `id` = B5m1C38HeChtbukyY3BUqwsMZ0zdOto0we6tsOnF limit 1) at D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback()
#1 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run()
#2 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3133): Illuminate\\Database\\Connection->select()
#3 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3118): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3706): Illuminate\\Database\\Query\\Builder->{closure:Illuminate\\Database\\Query\\Builder::get():3117}()
#5 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3117): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#6 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(344): Illuminate\\Database\\Query\\Builder->get()
#7 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3038): Illuminate\\Database\\Query\\Builder->first()
#8 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php(97): Illuminate\\Database\\Query\\Builder->find()
#9 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(114): Illuminate\\Session\\DatabaseSessionHandler->read()
#10 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(102): Illuminate\\Session\\Store->readFromHandler()
#11 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(86): Illuminate\\Session\\Store->loadSession()
#12 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(147): Illuminate\\Session\\Store->start()
#13 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(393): Illuminate\\Session\\Middleware\\StartSession->{closure:Illuminate\\Session\\Middleware\\StartSession::startSession():144}()
#14 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(144): tap()
#15 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(116): Illuminate\\Session\\Middleware\\StartSession->startSession()
#16 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#17 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#18 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#19 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#20 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#21 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#22 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#23 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#24 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#25 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#26 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#27 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch()
#28 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}()
#29 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}()
#30 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#31 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#32 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#33 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#34 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#35 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#36 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#37 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#38 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#39 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#40 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#41 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#42 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#43 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#44 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#45 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#46 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then()
#47 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#48 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1190): Illuminate\\Foundation\\Http\\Kernel->handle()
#49 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest()
#50 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#51 {main}

[previous exception] [object] (PDOException(code: 0): could not find driver at D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:67)
[stacktrace]
#0 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(67): PDO::connect()
#1 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#3 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->{closure:Illuminate\\Database\\Connectors\\ConnectionFactory::createPdoResolverWithHosts():180}()
#5 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func()
#6 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1267): Illuminate\\Database\\Connection->getPdo()
#7 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(512): Illuminate\\Database\\Connection->getReadPdo()
#8 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(407): Illuminate\\Database\\Connection->getPdoForSelect()
#9 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::select():398}()
#10 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback()
#11 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run()
#12 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3133): Illuminate\\Database\\Connection->select()
#13 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3118): Illuminate\\Database\\Query\\Builder->runSelect()
#14 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3706): Illuminate\\Database\\Query\\Builder->{closure:Illuminate\\Database\\Query\\Builder::get():3117}()
#15 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3117): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#16 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(344): Illuminate\\Database\\Query\\Builder->get()
#17 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3038): Illuminate\\Database\\Query\\Builder->first()
#18 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php(97): Illuminate\\Database\\Query\\Builder->find()
#19 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(114): Illuminate\\Session\\DatabaseSessionHandler->read()
#20 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(102): Illuminate\\Session\\Store->readFromHandler()
#21 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(86): Illuminate\\Session\\Store->loadSession()
#22 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(147): Illuminate\\Session\\Store->start()
#23 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(393): Illuminate\\Session\\Middleware\\StartSession->{closure:Illuminate\\Session\\Middleware\\StartSession::startSession():144}()
#24 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(144): tap()
#25 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(116): Illuminate\\Session\\Middleware\\StartSession->startSession()
#26 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#27 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#28 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#29 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#30 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#31 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#32 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#33 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#34 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#35 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#36 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#37 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch()
#38 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}()
#39 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}()
#40 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#41 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#42 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#43 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#44 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#45 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#46 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#47 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#48 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#49 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#50 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#51 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#52 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#53 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#54 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#55 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#56 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then()
#57 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#58 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1190): Illuminate\\Foundation\\Http\\Kernel->handle()
#59 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest()
#60 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#61 {main}
"} 
[2025-06-15 13:50:48] local.ERROR: could not find driver (Connection: mysql, SQL: select * from `sessions` where `id` = TLsMkrvPgWeqKqzX6m2ieNtPY7G6NVSKYsZDbD33 limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): could not find driver (Connection: mysql, SQL: select * from `sessions` where `id` = TLsMkrvPgWeqKqzX6m2ieNtPY7G6NVSKYsZDbD33 limit 1) at D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback()
#1 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run()
#2 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3133): Illuminate\\Database\\Connection->select()
#3 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3118): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3706): Illuminate\\Database\\Query\\Builder->{closure:Illuminate\\Database\\Query\\Builder::get():3117}()
#5 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3117): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#6 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(344): Illuminate\\Database\\Query\\Builder->get()
#7 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3038): Illuminate\\Database\\Query\\Builder->first()
#8 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php(97): Illuminate\\Database\\Query\\Builder->find()
#9 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(114): Illuminate\\Session\\DatabaseSessionHandler->read()
#10 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(102): Illuminate\\Session\\Store->readFromHandler()
#11 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(86): Illuminate\\Session\\Store->loadSession()
#12 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(147): Illuminate\\Session\\Store->start()
#13 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(393): Illuminate\\Session\\Middleware\\StartSession->{closure:Illuminate\\Session\\Middleware\\StartSession::startSession():144}()
#14 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(144): tap()
#15 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(116): Illuminate\\Session\\Middleware\\StartSession->startSession()
#16 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#17 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#18 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#19 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#20 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#21 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#22 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#23 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#24 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#25 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#26 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#27 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch()
#28 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}()
#29 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}()
#30 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#31 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#32 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#33 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#34 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#35 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#36 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#37 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#38 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#39 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#40 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#41 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#42 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#43 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#44 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#45 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#46 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then()
#47 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#48 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1190): Illuminate\\Foundation\\Http\\Kernel->handle()
#49 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest()
#50 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#51 {main}

[previous exception] [object] (PDOException(code: 0): could not find driver at D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:67)
[stacktrace]
#0 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(67): PDO::connect()
#1 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#3 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->{closure:Illuminate\\Database\\Connectors\\ConnectionFactory::createPdoResolverWithHosts():180}()
#5 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func()
#6 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1267): Illuminate\\Database\\Connection->getPdo()
#7 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(512): Illuminate\\Database\\Connection->getReadPdo()
#8 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(407): Illuminate\\Database\\Connection->getPdoForSelect()
#9 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::select():398}()
#10 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback()
#11 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run()
#12 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3133): Illuminate\\Database\\Connection->select()
#13 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3118): Illuminate\\Database\\Query\\Builder->runSelect()
#14 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3706): Illuminate\\Database\\Query\\Builder->{closure:Illuminate\\Database\\Query\\Builder::get():3117}()
#15 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3117): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#16 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(344): Illuminate\\Database\\Query\\Builder->get()
#17 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3038): Illuminate\\Database\\Query\\Builder->first()
#18 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php(97): Illuminate\\Database\\Query\\Builder->find()
#19 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(114): Illuminate\\Session\\DatabaseSessionHandler->read()
#20 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(102): Illuminate\\Session\\Store->readFromHandler()
#21 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(86): Illuminate\\Session\\Store->loadSession()
#22 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(147): Illuminate\\Session\\Store->start()
#23 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(393): Illuminate\\Session\\Middleware\\StartSession->{closure:Illuminate\\Session\\Middleware\\StartSession::startSession():144}()
#24 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(144): tap()
#25 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(116): Illuminate\\Session\\Middleware\\StartSession->startSession()
#26 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#27 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#28 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#29 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#30 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#31 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#32 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#33 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#34 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#35 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#36 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#37 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch()
#38 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}()
#39 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}()
#40 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#41 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#42 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#43 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#44 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#45 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#46 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#47 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#48 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#49 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#50 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#51 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#52 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#53 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#54 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#55 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#56 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then()
#57 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#58 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1190): Illuminate\\Foundation\\Http\\Kernel->handle()
#59 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest()
#60 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#61 {main}
"} 
[2025-06-15 13:50:49] local.ERROR: could not find driver {"exception":"[object] (PDOException(code: 0): could not find driver at D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:67)
[stacktrace]
#0 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(67): PDO::connect()
#1 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#3 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->{closure:Illuminate\\Database\\Connectors\\ConnectionFactory::createPdoResolverWithHosts():180}()
#5 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func()
#6 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(151): Illuminate\\Database\\Connection->getPdo()
#7 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(127): Illuminate\\Database\\Connection->createTransaction()
#8 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(26): Illuminate\\Database\\Connection->beginTransaction()
#9 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction()
#10 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(351): Illuminate\\Queue\\DatabaseQueue->pop()
#11 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->{closure:Illuminate\\Queue\\Worker::getNextJob():350}()
#12 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(327): Illuminate\\Queue\\Worker->getNextJob()
#13 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob()
#14 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker()
#15 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#16 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#17 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure()
#18 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#19 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call()
#20 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call()
#21 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#22 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()
#23 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#24 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#25 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#26 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run()
#27 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1205): Illuminate\\Foundation\\Console\\Kernel->handle()
#28 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\artisan(13): Illuminate\\Foundation\\Application->handleCommand()
#29 {main}
"} 
[2025-06-15 13:50:51] local.ERROR: could not find driver (Connection: mysql, SQL: select * from `sessions` where `id` = wDdvDMdOcbw36lptspRFwN4PDTxjcyYQRFsdKzYS limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): could not find driver (Connection: mysql, SQL: select * from `sessions` where `id` = wDdvDMdOcbw36lptspRFwN4PDTxjcyYQRFsdKzYS limit 1) at D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback()
#1 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run()
#2 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3133): Illuminate\\Database\\Connection->select()
#3 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3118): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3706): Illuminate\\Database\\Query\\Builder->{closure:Illuminate\\Database\\Query\\Builder::get():3117}()
#5 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3117): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#6 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(344): Illuminate\\Database\\Query\\Builder->get()
#7 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3038): Illuminate\\Database\\Query\\Builder->first()
#8 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php(97): Illuminate\\Database\\Query\\Builder->find()
#9 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(114): Illuminate\\Session\\DatabaseSessionHandler->read()
#10 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(102): Illuminate\\Session\\Store->readFromHandler()
#11 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(86): Illuminate\\Session\\Store->loadSession()
#12 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(147): Illuminate\\Session\\Store->start()
#13 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(393): Illuminate\\Session\\Middleware\\StartSession->{closure:Illuminate\\Session\\Middleware\\StartSession::startSession():144}()
#14 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(144): tap()
#15 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(116): Illuminate\\Session\\Middleware\\StartSession->startSession()
#16 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#17 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#18 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#19 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#20 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#21 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#22 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#23 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#24 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#25 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#26 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#27 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch()
#28 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}()
#29 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}()
#30 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#31 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#32 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#33 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#34 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#35 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#36 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#37 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#38 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#39 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#40 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#41 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#42 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#43 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#44 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#45 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#46 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then()
#47 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#48 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1190): Illuminate\\Foundation\\Http\\Kernel->handle()
#49 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest()
#50 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#51 {main}

[previous exception] [object] (PDOException(code: 0): could not find driver at D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:67)
[stacktrace]
#0 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(67): PDO::connect()
#1 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#3 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->{closure:Illuminate\\Database\\Connectors\\ConnectionFactory::createPdoResolverWithHosts():180}()
#5 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func()
#6 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1267): Illuminate\\Database\\Connection->getPdo()
#7 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(512): Illuminate\\Database\\Connection->getReadPdo()
#8 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(407): Illuminate\\Database\\Connection->getPdoForSelect()
#9 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::select():398}()
#10 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback()
#11 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run()
#12 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3133): Illuminate\\Database\\Connection->select()
#13 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3118): Illuminate\\Database\\Query\\Builder->runSelect()
#14 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3706): Illuminate\\Database\\Query\\Builder->{closure:Illuminate\\Database\\Query\\Builder::get():3117}()
#15 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3117): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#16 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(344): Illuminate\\Database\\Query\\Builder->get()
#17 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3038): Illuminate\\Database\\Query\\Builder->first()
#18 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php(97): Illuminate\\Database\\Query\\Builder->find()
#19 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(114): Illuminate\\Session\\DatabaseSessionHandler->read()
#20 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(102): Illuminate\\Session\\Store->readFromHandler()
#21 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(86): Illuminate\\Session\\Store->loadSession()
#22 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(147): Illuminate\\Session\\Store->start()
#23 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(393): Illuminate\\Session\\Middleware\\StartSession->{closure:Illuminate\\Session\\Middleware\\StartSession::startSession():144}()
#24 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(144): tap()
#25 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(116): Illuminate\\Session\\Middleware\\StartSession->startSession()
#26 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#27 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#28 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#29 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#30 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#31 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#32 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#33 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#34 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#35 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#36 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#37 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch()
#38 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}()
#39 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}()
#40 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#41 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#42 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#43 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#44 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#45 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#46 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#47 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#48 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#49 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#50 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#51 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#52 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#53 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#54 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#55 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}()
#56 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then()
#57 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#58 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1190): Illuminate\\Foundation\\Http\\Kernel->handle()
#59 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest()
#60 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#61 {main}
"} 
[2025-06-15 13:50:54] local.ERROR: could not find driver {"exception":"[object] (PDOException(code: 0): could not find driver at D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:67)
[stacktrace]
#0 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(67): PDO::connect()
#1 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#3 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->{closure:Illuminate\\Database\\Connectors\\ConnectionFactory::createPdoResolverWithHosts():180}()
#5 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func()
#6 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(151): Illuminate\\Database\\Connection->getPdo()
#7 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(127): Illuminate\\Database\\Connection->createTransaction()
#8 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(26): Illuminate\\Database\\Connection->beginTransaction()
#9 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction()
#10 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(351): Illuminate\\Queue\\DatabaseQueue->pop()
#11 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->{closure:Illuminate\\Queue\\Worker::getNextJob():350}()
#12 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(327): Illuminate\\Queue\\Worker->getNextJob()
#13 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob()
#14 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker()
#15 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#16 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#17 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure()
#18 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#19 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call()
#20 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call()
#21 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#22 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()
#23 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#24 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#25 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#26 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run()
#27 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1205): Illuminate\\Foundation\\Console\\Kernel->handle()
#28 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\artisan(13): Illuminate\\Foundation\\Application->handleCommand()
#29 {main}
"} 
[2025-06-15 13:50:58] local.ERROR: could not find driver {"exception":"[object] (PDOException(code: 0): could not find driver at D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:67)
[stacktrace]
#0 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(67): PDO::connect()
#1 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#3 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->{closure:Illuminate\\Database\\Connectors\\ConnectionFactory::createPdoResolverWithHosts():180}()
#5 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func()
#6 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(151): Illuminate\\Database\\Connection->getPdo()
#7 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(127): Illuminate\\Database\\Connection->createTransaction()
#8 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(26): Illuminate\\Database\\Connection->beginTransaction()
#9 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction()
#10 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(351): Illuminate\\Queue\\DatabaseQueue->pop()
#11 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->{closure:Illuminate\\Queue\\Worker::getNextJob():350}()
#12 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(327): Illuminate\\Queue\\Worker->getNextJob()
#13 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob()
#14 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker()
#15 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#16 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#17 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure()
#18 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#19 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call()
#20 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call()
#21 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#22 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()
#23 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#24 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#25 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#26 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run()
#27 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1205): Illuminate\\Foundation\\Console\\Kernel->handle()
#28 D:\\SEMESTER 5\\Desain Front-end\\perpindahan-penduduk\\artisan(13): Illuminate\\Foundation\\Application->handleCommand()
#29 {main}
"} 
