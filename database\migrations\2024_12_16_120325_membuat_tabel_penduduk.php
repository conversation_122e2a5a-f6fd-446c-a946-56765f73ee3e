<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('penduduk', function (Blueprint $table) {
            $table->id();
            $table->string('nik');
            $table->string('nama');
            $table->string('lahir');
            $table->date('tanggal');
            $table->string('kelamin');
            $table->string('agama');
            $table->string('kawin')->nullable();
            $table->string('pendidikan');
            $table->string('pekerjaan');
            $table->string('kewarganegaran');
            $table->string('alamat');
            $table->string('hubungan')->nullable();
            $table->foreignId('keluarga_id')->nullable()->constrained('keluargas')->cascadeOnDelete();
            $table->timestamps();
            $table->string('telepon')->nullable();
            $table->string('darah')->nullable();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('penduduk', function (Blueprint $table) {
            //
        });
    }
};
