<?php

namespace App\Http\Controllers;

use App\Models\Keluarga;
use App\Models\Penduduk;
use Illuminate\Http\Request;
use Inertia\Inertia;




class TrashController extends Controller
{
    //
    public function indexKeluarga()
    {
        $keluargas = Keluarga::onlyTrashed()->get();
        return Inertia::render('Trash/KeluargaTrash', [
            'keluargas' => $keluargas
        ]);
    }

    public function destroyKeluarga($id)
    {
        $keluarga = Keluarga::findOrFail($id); // Cari berdasarkan ID
        $keluarga->forceDelete();

        return redirect()->route('trash.keluarga')->with('success', 'Keluarga berhasil dihapus.');
    }

    public function indexPenduduk()
    {
        $penduduks = Penduduk::onlyTrashed()->get();
        return Inertia::render('Trash/PendudukTrash', [
            'penduduks' => $penduduks
        ]);
    }
}
