<?php

declare(strict_types=1);

/*
 * This file is part of the league/commonmark package.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * Original code based on the CommonMark JS reference parser (https://bitly.com/commonmark-js)
 *  - (c) <PERSON>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace League\CommonMark\Reference;

/**
 * A collection of references
 *
 * @phpstan-extends \IteratorAggregate<ReferenceInterface>
 */
interface ReferenceMapInterface extends \IteratorAggregate, \Countable
{
    public function add(ReferenceInterface $reference): void;

    public function contains(string $label): bool;

    public function get(string $label): ?ReferenceInterface;
}
