<?php

namespace Database\Seeders;
use App\Models\Keluarga;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DefaultSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        //
        Keluarga::create([
            'no_kk' => '0000000000000000', // Nomor KK
            'kepala_keluarga' => '-', // Nama <PERSON>
            'provinsi' => 'Jawa Barat', // Provinsi
            'kabupaten' => 'Bandung', // Kabupaten
            'kecamatan' => 'Cicadas', // Kecamatan
            'desa' => 'Desa A', // Desa
            'rt' => '001', // RT
            'rw' => '002', // RW
            'alamat' => 'Jl. Raya No. 1, Cicadas, Bandung', // Alamat
            'kode_pos' => '40111', // Kode Pos
        ]);
    }
}
