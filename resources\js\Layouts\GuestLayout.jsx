import ApplicationLogo from '@/Components/ApplicationLogo';
import { Link } from '@inertiajs/react';

export default function GuestLayout({ children }) {
    return (
        <div className="absolute top-0 right-0 bottom-0 left-0 overflow-hidden flex min-h-screen flex-col items-center pt-6 sm:justify-center sm:pt-0">
            <div
                className="absolute inset-0 bg-cover bg-center"
                style={{
                    backgroundImage: `url('https://www.realoka.com/pictures/5/rumah-siap-huni-dalam-perumahan-bale-wangsa-426863ia8z9.jpeg')`,
                    filter: 'blur(5px)',
                }}
            ></div>
            
            <div className='relative'>
                <Link href="/">
                    <ApplicationLogo className="h-64 rounded-full object-cover fill-current text-gray-500" />
                </Link>
            </div>
                <div className="relative text-center mb-6">
                        <h1 className="text-white text-2xl font-bold mt-2">
                            BALE MULIA RESIDENCE
                        </h1>
                </div>
            <div className="relative mt-6 w-full overflow-y-auto bg-white px-6 py-4 shadow-md sm:max-w-md sm:rounded-lg dark:bg-gray-800">
                {children}
            </div>
        </div>
    );
}
