import React from 'react'

const DetailKK = ({closeModal,selectedData}) => {
    console.log(selectedData.penduduk)
  return (
    <div>
        <div className='flex justify-between items-center'>
            <div className='pl-7'>
                <img 
                src="https://www.freeiconspng.com/thumbs/garuda/logo-garuda-pancasila-bw-hitam-putih-background-black-and-white-11.png" 
                alt="Logo Garuda Pancasila" 
                className="w-20 "
                />
            </div>
            <div className='flex flex-col justify-center items-center'>
                    <div className='text-4xl font-extrabold'>KARTU KELUARGA</div>
                    <div className='text-2xl font-semibold'>No. {selectedData.no_kk}</div>  
            </div>
            <div className='w-20'></div>
            
        </div>
        <div className='grid grid-cols-2 pl-10 mt-2'>
            <div>
                <table className='text-xs'>
                    <tbody>
                        <tr className="leading-none">
                            <td className="text-xs"><PERSON><PERSON></td>
                            <td className="pl-4">:</td> {/* Menambahkan padding horizontal */}
                            <td>{selectedData.kepala_keluarga}</td>
                        </tr>
                        <tr className="leading-none">
                            <td>Alamat</td>
                            <td className="pl-4">:</td> {/* Menambahkan padding horizontal */}
                            <td>{selectedData.alamat}</td>
                        </tr>
                        <tr className="leading-none">
                            <td>RT/RW</td>
                            <td className="pl-4">:</td> {/* Menambahkan padding horizontal */}
                            <td>{selectedData.rt}/{selectedData.rw}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div className='pl-20'>
                <table className='text-xs'>
                    <tbody>
                        <tr className="leading-none">
                            <td className="text-xs">Kecamatan</td>
                            <td className="pl-4">:</td> {/* Menambahkan padding horizontal */}
                            <td>{selectedData.kecamatan}</td>
                        </tr>
                        <tr className="leading-none">
                            <td>Kabupaten/Kota</td>
                            <td className="pl-4">:</td> {/* Menambahkan padding horizontal */}
                            <td>{selectedData.kabupaten}</td>
                        </tr>
                        <tr className="leading-none">
                            <td>Provinsi</td>
                            <td className="pl-4">:</td> {/* Menambahkan padding horizontal */}
                            <td>{selectedData.provinsi}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div className='mt-6'>
        <table className="table-auto border-collapse border-2 border-black w-full text-left">
        <thead className="">
            <tr className='leading-none'>
                <th className="border border-black text-xs px-2 py-1">No</th>
                <th className="border border-black text-xs px-2 py-1">Nama Lengkap</th>
                <th className="border border-black text-xs px-2 py-1">NIK</th>
                <th className="border border-black text-xs px-2 py-1">Jenis Kelamin</th>
                <th className="border border-black text-xs px-2 py-1">Tempat Lahir</th>
                <th className="border border-black text-xs px-2 py-1">Tanggal Lahir</th>
                <th className="border border-black text-xs px-2 py-1">Agama</th>
                
            </tr>
        </thead>
        <tbody>
        {selectedData.penduduk.map((penduduk, index) => (
            <tr key={penduduk.id} className="leading-none">
                <td className="border border-black text-xs px-2 py-1">{index + 1}</td>
                <td className="border border-black text-xs px-2 py-1">{penduduk.nama}</td>
                <td className="border border-black text-xs px-2 py-1">{penduduk.nik}</td>
                <td className="border border-black text-xs px-2 py-1">{penduduk.kelamin}</td>
                <td className="border border-black text-xs px-2 py-1">{penduduk.lahir}</td>
                <td className="border border-black text-xs px-2 py-1">{penduduk.tanggal}</td>
                <td className="border border-black text-xs px-2 py-1">{penduduk.agama}</td>
                
            </tr>
        ))}
        </tbody>
    </table>
        </div>
        <div className='mt-2'>
        <table className="table-auto border-collapse border-2 border-black w-full text-left">
        <thead className="">
            
                <tr className='leading-none'>
                <th className="border border-black text-xs px-2 py-1">No</th>
                <th className="border border-black text-xs px-2 py-1">Pendidikan</th>
                <th className="border border-black text-xs px-2 py-1">Jenis Pekerjaan</th>
                <th className="border border-black text-xs px-2 py-1">Status Perkawinan</th>
                <th className="border border-black text-xs px-2 py-1">Status Hubungan Dalam Keluarga</th>
                <th className="border border-black text-xs px-2 py-1">Kewarganegaraan</th>
                
            </tr>
        </thead>
        <tbody>
        {selectedData.penduduk.map((penduduk, index) => (
            <tr key={penduduk.id} className="leading-none">
                <td className="border border-black text-xs px-2 py-1">{index + 1}</td>
                <td className="border border-black text-xs px-2 py-1">{penduduk.pendidikan}</td>
                <td className="border border-black text-xs px-2 py-1">{penduduk.pekerjaan}</td>
                <td className="border border-black text-xs px-2 py-1">{penduduk.kawin}</td>
                <td className="border border-black text-xs px-2 py-1">{penduduk.hubungan}</td>
                <td className="border border-black text-xs px-2 py-1">{penduduk.kewarganegaran}</td>
            </tr>
        ))}
        </tbody>
    </table>

        </div>
    </div>

  )
}

export default DetailKK