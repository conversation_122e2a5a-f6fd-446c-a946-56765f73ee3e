import React,{ useState, useEffect} from 'react'
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout'
import { Head,router} from '@inertiajs/react'
import toast from 'react-hot-toast';

const Edit = ({keluarga}) => {
    const [formData, setFormData] = useState({
        no_kk: '',
        kepala_keluarga: '',
        desa: '',
        desa_id: '',
        kecamatan: '',
        kecamatan_id: '',
        kabupaten: '',
        kabupaten_id: '',
        provinsi: '',
        provinsi_id: '',
        rw: '',
        rt: '',
        alamat: '',
        kode_pos: '',
        kode_pos_id: '',
      });
      
    
      useEffect(() => {
        setFormData({
          no_kk: keluarga.no_kk || '',
          kepala_keluarga: keluarga.kepala_keluarga || '',
          desa: keluarga.desa || '',
          kecamatan: keluarga.kecamatan || '',
          kabupaten: keluarga.kabupaten || '',
          provinsi: keluarga.provinsi || '',
          rw: keluarga.rw || '',
          rt: keluarga.rt || '',
          kode_pos: keluarga.kode_pos || '',
          alamat: keluarga.alamat || '',
        });
      }, [keluarga]);

      const [provinsi, setProvinsi] = useState([]);
      useEffect(() => {
        fetch('https://alamat.thecloudalert.com/api/provinsi/get/')
          .then((response) => response.json())
          .then((data) => {
            setProvinsi(data.result);
          });
      },[]);

      useEffect(() => {
        if (formData.provinsi) {
          const foundProvinsi = provinsi.find(
            (prov) => prov.text === formData.provinsi // Sesuaikan dengan field yang digunakan
          );
          if (foundProvinsi) {
            setFormData((prevData) => ({
              ...prevData,
              provinsi_id: foundProvinsi.id, // Gantilah 'id' dengan field yang sesuai
            }));
          }
        }
      }, [provinsi, formData.provinsi]);
    

    const [kabupaten, setKabupaten] = useState([]);

    useEffect(() => {
        if (formData.kabupaten) {
          const foundKabupaten = kabupaten.find(
            (kab) => kab.text === formData.kabupaten // Sesuaikan dengan field yang digunakan
          );
          if (foundKabupaten) {
            setFormData((prevData) => ({
              ...prevData,
              kabupaten_id: foundKabupaten.id, // Gantilah 'id' dengan field yang sesuai
            }));
          }
        }
      }, [kabupaten, formData.kabupaten]);


    useEffect(() => {
    // Hanya jalankan fetch jika provinsi_id telah diisi
    if (formData.provinsi_id) {
        fetch(`https://alamat.thecloudalert.com/api/kabkota/get/?d_provinsi_id=${formData.provinsi_id}`)
        .then((response) => response.json())
        .then((data) => {
            setKabupaten(data.result);
        })
        .catch((error) => {
            console.error("Error fetching kabupaten data:", error);
        });
    } else {
        // Reset kabupaten jika provinsi_id kosong
        setKabupaten([]);
    }
    }, [formData.provinsi_id]);

    
    


    const [kecamatan, setKecamatan] = useState([]);

    useEffect(() => {
        if (formData.kecamatan) {
          const foundKecamatan = kecamatan.find(
            (kec) => kec.text === formData.kecamatan // Sesuaikan dengan field yang digunakan
          );
          if (foundKecamatan) {
            setFormData((prevData) => ({
              ...prevData,
              kecamatan_id: foundKecamatan.id, // Gantilah 'id' dengan field yang sesuai
            }));
          }
        }
      }, [kecamatan, formData.kecamatan]);

    useEffect(() => {
    // Hanya jalankan fetch jika provinsi_id telah diisi
    if (formData.kabupaten_id) {
        fetch(`https://alamat.thecloudalert.com/api/kecamatan/get/?d_kabkota_id=${formData.kabupaten_id}`)
        .then((response) => response.json())
        .then((data) => {
            setKecamatan(data.result);
        })
        .catch((error) => {
            console.error("Error fetching kabupaten data:", error);
        });
    } else {
        // Reset kabupaten jika provinsi_id kosong
        setKecamatan([]);
    }
    }, [formData.kabupaten_id]);

    
    const [desa, setDesa] = useState([]);

    useEffect(() => {
        if (formData.desa) {
          const foundDesa = desa.find(
            (desa) => desa.text === formData.desa // Sesuaikan dengan field yang digunakan
          );
          if (foundDesa) {
            setFormData((prevData) => ({
              ...prevData,
              desa_id: foundDesa.id, // Gantilah 'id' dengan field yang sesuai
            }));
          }
        }
      }, [desa, formData.desa]);

    useEffect(() => {
    // Hanya jalankan fetch jika provinsi_id telah diisi
    if (formData.kecamatan_id) {
        fetch(`https://alamat.thecloudalert.com/api/kelurahan/get/?d_kecamatan_id=${formData.kecamatan_id}`)
        .then((response) => response.json())
        .then((data) => {
            setDesa(data.result);
        })
        .catch((error) => {
            console.error("Error fetching kabupaten data:", error);
        });
    } else {
        // Reset kabupaten jika provinsi_id kosong
        setDesa([]);
    }
    }, [formData.kecamatan_id]);

    const [pos, setPos] = useState([]);

    useEffect(() => {
        if (formData.kode_pos) {
          const foundDesa = desa.find(
            (desa) => desa.text === formData.kode_pos // Sesuaikan dengan field yang digunakan
          );
          if (foundDesa) {
            console.log('kode_pos = ',foundDesa);
          }
        }
      }, [desa, formData.desa]);
    
    console.log("pos",pos)
    console.log("pos_id",formData.kode_pos_id)
    useEffect(() => {
        // Pastikan kabupaten_id, kecamatan_id, dan desa_id sudah terisi sebelum melakukan fetch
        if (formData.desa_id && formData.kabupaten_id && formData.kecamatan_id) {
            fetch(`https://alamat.thecloudalert.com/api/kodepos/get/?d_kabkota_id=${formData.kabupaten_id}&d_kecamatan_id=${formData.kecamatan_id}`)
            .then((response) => response.json())
            .then((data) => {
                setPos(data.result);
            })
            .catch((error) => {
                console.error("Error fetching kabupaten data:", error);
            });
        } else {
            // Reset pos jika salah satu data tidak ada
            setPos([]);
        }
    }, [formData.desa_id, formData.kecamatan_id, formData.kabupaten_id]);
    

      // Fungsi untuk mengubah nilai input form
      const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData((prevData) => ({
          ...prevData,
          [name]: value,
        }));
      };
    
      // Fungsi untuk mengirim data form
      const handleEdit = (e) => {
        e.preventDefault();
        if (
            !formData.no_kk || 
            !formData.kepala_keluarga || 
            !formData.provinsi || 
            !formData.kabupaten || 
            !formData.kecamatan || 
            !formData.desa || 
            !formData.rt || 
            !formData.rw || 
            !formData.kode_pos || 
            !formData.alamat 
        ) {
            toast.error('Semua field harus diisi!');
            return;
        } else if (formData.no_kk.length !== 16) {
            toast.error('Nomer Kartu Keluarga harus 16 karakter!');
            return;
        } else if (!/^\d+$/.test(formData.no_kk)) { // Validasi angka menggunakan regex
            toast.error('Nomer Kartu Keluarga harus berupa angka!');
            return;
        }
        
        
          // Jika semua validasi lolos, lakukan submit data
        router.put(`/kartu-keluarga/${keluarga.id}`, formData, {
            onSuccess: () => {
                toast.success('Data berhasil di ubah!');
                router.visit('/kartu-keluarga'); // Redirect setelah sukses
            },
            onError: (errors) => {
                console.log('Error occurred:', errors);
                toast.error('Terjadi kesalahan saat menyimpan data!');
            },
        });

      }    
  return (
    <AuthenticatedLayout
    header={
        <h2 className="text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200">
            Kartu Keluarga
        </h2>
    }
    >
    <Head title="Tambah Kartu Keluarga" />
    <div className='bg-gray-100 dark:bg-gray-900 overflow-y-auto'>
    <div className="bg-white p-6 rounded-lg shadow-lg max-w-3xl mx-auto  dark:bg-gray-700 mt-10">
      <h2 className="text-xl font-bold mb-4 dark:text-white text-center">Edit Data Penduduk</h2>
      <form onSubmit={handleEdit}>
        <div className="mb-4">
          <label className="block text-sm font-semibold dark:text-gray-100" htmlFor="no_kk">
            Nomer Kartu Keluarga
          </label>
          <input
            type="text"
            id="no_kk"
            name="no_kk"
            value={formData.no_kk}
            onChange={handleInputChange}
            maxLength={16}
            className="mt-1 p-2 border bg-gray-100 border-gray-300 rounded w-full dark:bg-gray-300 dark:border-gray-300 dark:text-gray-900"
            disabled
          />
        </div>

        <div className="mb-4">
          <label className="block text-sm font-semibold dark:text-gray-100" htmlFor="kepala_keluarga">
            Kepala Keluarga
          </label>
          <input
            type="text"
            id="kepala_keluarga"
            name="kepala_keluarga"
            value={formData.kepala_keluarga}
            onChange={handleInputChange}
            className="mt-1 p-2 border border-gray-300 rounded w-full dark:bg-gray-200 dark:border-gray-200 dark:text-gray-900"
          
          />
        </div>
            <div className="mb-4">
            <label className="block text-sm font-semibold dark:text-gray-100" htmlFor="provinsi">
                Provinsi
            </label>
            <select
                id="provinsi"
                name="provinsi"
                value={formData.provinsi_id}
                onChange={(e) => {
                    const selectedId = e.target.value; // Ambil id dari opsi yang dipilih
                    const selectedText = provinsi.find((item) => item.id === selectedId)?.text; // Cari teks provinsi berdasarkan id
                    
                    // Perbarui state formData
                    setFormData({
                      ...formData,
                      provinsi_id: selectedId, // Simpan id provinsi
                      provinsi: selectedText, // Simpan nama provinsi
                    });
                  }}
                className="mt-1 p-2 border border-gray-300 rounded w-full dark:bg-gray-200 dark:border-gray-200 dark:text-gray-900"
              
            >   
                <option value="" disabled>
                    Pilih Provinsi
                </option>
                {provinsi?.map((item) => (
                <option key={item.id} value={item.id}>
                    {item.text}
                </option>
                ))}

            </select>
            </div>
            <div className="mb-4">
            <label className="block text-sm font-semibold dark:text-gray-100" htmlFor="kabupaten">
                Kota/Kabupaten
            </label>
            <select
                id="kabupaten"
                name="kabupaten"
                value={formData.kabupaten_id}
                onChange={(e) => {
                    const selectedId = e.target.value; // Ambil id dari opsi yang dipilih
                    const selectedText = kabupaten.find((item) => item.id === selectedId)?.text; // Cari teks provinsi berdasarkan id
                    
                    // Perbarui state formData
                    setFormData({
                      ...formData,
                      kabupaten_id: selectedId, // Simpan id provinsi
                      kabupaten: selectedText, // Simpan nama provinsi
                    });
                  }}
                className="mt-1 p-2 border border-gray-300 rounded w-full dark:bg-gray-200 dark:border-gray-200 dark:text-gray-900"
              
            >
            {kabupaten.length === 0 ? (
            <option value="" disabled>
                Pilih provinsi terlebih dahulu
            </option>
            ) : (
            <>
                <option value="" disabled>
                Pilih Kabupaten
                </option>
                {kabupaten.map((item) => (
                <option key={item.id} value={item.id}>
                    {item.text}
                </option>
                ))}
            </>
            )}

            </select>
            </div>
            <div className="mb-4">
            <label className="block text-sm font-semibold dark:text-gray-100" htmlFor="kecamatan">
                Kecamatan
            </label>
            <select
                id="kecamatan"
                name="kecamatan"
                value={formData.kecamatan_id}
                onChange={(e) => {
                    const selectedId = e.target.value; // Ambil id dari opsi yang dipilih
                    const selectedText = kecamatan.find((item) => item.id === selectedId)?.text; // Cari teks provinsi berdasarkan id
                    
                    // Perbarui state formData
                    setFormData({
                      ...formData,
                      kecamatan_id: selectedId, // Simpan id provinsi
                      kecamatan: selectedText, // Simpan nama provinsi
                    });
                  }}
                className="mt-1 p-2 border border-gray-300 rounded w-full dark:bg-gray-200 dark:border-gray-200 dark:text-gray-900"
              
            >
            {(kabupaten.length === 0 ? (
            <option value="" disabled>
                Pilih Provinsi terlebih dahulu
            </option>
            ) : ( kecamatan.length === 0 ? (
                <option value="" disabled>
                    Pilih Kabupaten terlebih dahulu
                </option>
            ) : (
                <>
                <option value="" disabled>
                Pilih Kecamatan
                </option>
                {kecamatan.map((item) => (
                <option key={item.id} value={item.id}>
                    {item.text}
                </option>
                ))}
            </>
            )
            
            )
            )}

            </select>
            </div>
            <div className="mb-4">
            <label className="block text-sm font-semibold dark:text-gray-100" htmlFor="desa">
                Kelurahan/Desa
            </label>
            <select
                id="desa"
                name="desa"
                value={formData.desa_id}
                onChange={(e) => {
                    const selectedId = e.target.value; // Ambil id dari opsi yang dipilih
                    const selectedText = desa.find((item) => item.id === selectedId)?.text; // Cari teks provinsi berdasarkan id
                    
                    // Perbarui state formData
                    setFormData({
                      ...formData,
                      desa_id: selectedId, // Simpan id provinsi
                      desa: selectedText, // Simpan nama provinsi
                    });
                  }}
                className="mt-1 p-2 border border-gray-300 rounded w-full dark:bg-gray-200 dark:border-gray-200 dark:text-gray-900"
              
            >
            {(kabupaten.length === 0 ? (
            <option value="" disabled>
                Pilih Provinsi terlebih dahulu
            </option>
            ) : (kecamatan.length === 0 ? (
                <option value="" disabled>
                    Pilih Kabupaten terlebih dahulu
                </option>
            ) : ( desa.length === 0 ? (
                <option value="" disabled>
                    Pilih Kecamatan terlebih dahulu
                </option>
            ) : (
                <>
                <option value="" disabled>
                Pilih Kelurahan/Desa
                </option>
                {desa.map((item) => (
                <option key={item.id} value={item.id}>
                    {item.text}
                </option>
                ))}
            </>
            )
                
            )
            
            )
            )}
            </select>
        </div>
        
        <div className='flex mb-4 gap-3'>
        <div className="w-1/2">
          <label className="block text-sm font-semibold dark:text-gray-100" htmlFor="rt">
            RT
          </label>
          <input
            type="text"
            id="rt"
            name="rt"
            value={formData.rt}
            onChange={handleInputChange}
            className="mt-1 p-2 border border-gray-300 rounded w-full dark:bg-gray-200 dark:border-gray-200 dark:text-gray-900"
          
          />
        </div>

        <div className="w-1/2">
          <label className="block text-sm font-semibold dark:text-gray-100" htmlFor="rw">
            RW
          </label>
          <input
            type="text"
            id="rw"
            name="rw"
            value={formData.rw}
            onChange={handleInputChange}
            className="mt-1 p-2 border border-gray-300 rounded w-full dark:bg-gray-200 dark:border-gray-200 dark:text-gray-900"
          
          />
        </div>
        </div>
        <div className="mb-4">
          <label className="block text-sm font-semibold dark:text-gray-100" htmlFor="alamat">
            Alamat
          </label>
          <input
            type="text"
            id="alamat"
            name="alamat"
            value={formData.alamat}
            onChange={handleInputChange}
            className="mt-1 p-2 border border-gray-300 rounded w-full dark:bg-gray-200 dark:border-gray-200 dark:text-gray-900"
          
          />
        </div>
        <div className="mb-4">
          <label className="block text-sm font-semibold dark:text-gray-100" htmlFor="kode_pos">
            Kode Pos
          </label>
          <select
                id="kode_pos"
                name="kode_pos"
                value={formData.kode_pos_id}
                onChange={(e) => {
                    const selectedId = e.target.value;
                    const selectedText = pos.find((item) => item.id === selectedId)?.text; 
                    setFormData({
                      ...formData,
                      kode_pos_id: selectedId, // Simpan id provinsi
                      kode_pos: selectedText, // Simpan nama provinsi
                    });
                  }}
                className="mt-1 p-2 border border-gray-300 rounded w-full dark:bg-gray-200 dark:border-gray-200 dark:text-gray-900"
              
            >
                <option value={formData.kode_pos}>
                {formData.kode_pos}
                </option>
                {pos.map((item) => (
                <option key={item.id} value={item.id}>
                    {item.text}
                </option>
                ))}
            </select>
        </div>
        <button
          type="submit"
          className="mt-4 px-4 py-2 bg-blue-500 text-white dark:text-gray-100 rounded-lg"
        >
          Simpan
        </button>
      </form>
    </div>
    </div>
    </AuthenticatedLayout>
  )
}

export default Edit