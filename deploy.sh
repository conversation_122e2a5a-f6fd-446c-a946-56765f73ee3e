#!/bin/bash

# Deployment Script untuk VPS
echo "🚀 Starting deployment process..."

# 1. Update system packages
echo "📦 Updating system packages..."
sudo apt update && sudo apt upgrade -y

# 2. Install required packages
echo "🔧 Installing required packages..."
sudo apt install -y nginx mysql-server php8.2-fpm php8.2-mysql php8.2-xml php8.2-curl php8.2-zip php8.2-mbstring php8.2-gd composer nodejs npm git

# 3. Clone repository (if not exists)
if [ ! -d "/var/www/perpindahan-penduduk" ]; then
    echo "📥 Cloning repository..."
    sudo git clone YOUR_REPOSITORY_URL /var/www/perpindahan-penduduk
fi

# 4. Set permissions
echo "🔐 Setting permissions..."
sudo chown -R www-data:www-data /var/www/perpindahan-penduduk
sudo chmod -R 755 /var/www/perpindahan-penduduk
sudo chmod -R 775 /var/www/perpindahan-penduduk/storage
sudo chmod -R 775 /var/www/perpindahan-penduduk/bootstrap/cache

# 5. Install PHP dependencies
echo "📚 Installing PHP dependencies..."
cd /var/www/perpindahan-penduduk
sudo -u www-data composer install --optimize-autoloader --no-dev

# 6. Install Node.js dependencies and build
echo "🎨 Building frontend assets..."
sudo -u www-data npm install
sudo -u www-data npm run build

# 7. Environment setup
echo "⚙️ Setting up environment..."
sudo -u www-data cp .env.production .env

# 8. Generate application key
echo "🔑 Generating application key..."
sudo -u www-data php artisan key:generate

# 9. Run migrations
echo "🗄️ Running database migrations..."
sudo -u www-data php artisan migrate --force

# 10. Optimize Laravel
echo "⚡ Optimizing Laravel..."
sudo -u www-data php artisan config:cache
sudo -u www-data php artisan route:cache
sudo -u www-data php artisan view:cache

# 11. Create storage link
echo "🔗 Creating storage link..."
sudo -u www-data php artisan storage:link

echo "✅ Deployment completed!"
echo "🌐 Configure your Nginx and SSL certificate next."
