import React from 'react'
import { <PERSON><PERSON><PERSON>, Pie, Cell, Responsive<PERSON><PERSON>r, <PERSON><PERSON><PERSON>, Legend } from 'recharts';

export const Bulat = ({lakiLaki, perempuan}) => {
  const data = [
    { name: 'Laki-laki', value: laki<PERSON><PERSON> },
    { name: 'Perempuan', value: perempuan },
  ];
  
  const COLORS = ['#8884d8', '#82ca9d'];
  return (
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={data}
                dataKey="value"
                nameKey="name"
                outerRadius="80%"
                label
                fill="#8884d8"
              >
                {data.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
    );
}