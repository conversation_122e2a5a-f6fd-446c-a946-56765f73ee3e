<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Keluarga extends Model
{
    use HasFactory;

    protected $table = 'keluargas'; // Sesuaikan jika tabel tetap menggunakan 'keluargas'
    protected $dates = ['deleted_at'];

    protected $fillable = [
        'no_kk',
        'kepala_keluarga',
        'provinsi',
        'kabupaten',
        'kecamatan',
        'desa',
        'rt',
        'rw',
        'alamat',
        'kode_pos',
    ];

    public function penduduk()
    {
        return $this->hasMany(Penduduk::class);
    }
    
}
