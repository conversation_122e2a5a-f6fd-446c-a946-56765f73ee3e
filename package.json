{"private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite"}, "devDependencies": {"@headlessui/react": "^2.0.0", "@inertiajs/react": "^1.0.0", "@tailwindcss/forms": "^0.5.3", "@vitejs/plugin-react": "^4.2.0", "autoprefixer": "^10.4.12", "axios": "^1.7.4", "concurrently": "^9.0.1", "laravel-vite-plugin": "^1.0", "postcss": "^8.4.31", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwindcss": "^3.2.1", "vite": "^6.0"}, "dependencies": {"react-data-table": "^0.1.0", "react-data-table-component": "^7.6.2", "react-hot-toast": "^2.4.1", "react-icons": "^5.4.0", "react-modal": "^3.16.1", "react-phone-input-2": "^2.15.1", "react-phone-number-input": "^3.4.10", "recharts": "^2.15.0"}}