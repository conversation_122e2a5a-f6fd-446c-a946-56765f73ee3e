import React from 'react'
import Modal from 'react-modal';

const DeleteConfirmationModal = ({ isOpen, onRequestClose, onConfirm, id,message = "Apakah Anda yakin ingin menghapus data ini?" }) => {
    const customStyles = {
        content: {
            width: '300px',   // Ukuran modal yang lebih kecil
            height: '200px',   // Tinggi otomatis sesuai dengan konten
            margin: 'auto',   // Menempatkan modal di tengah layar
            padding: '20px',  // Padding di dalam modal
            borderRadius: '10px',  // Sudut modal yang membulat
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
        },
        overlay: {
              // Warna latar belakang overlay yang lebih transparan
            backgroundColor: 'rgba(0, 0, 0, 0.1)',
            zIndex: 9999,  // Menjaga modal tetap di atas konten lain
        }
    };
    return (
        
            <Modal
                isOpen={isOpen}
                onRequestClose={onRequestClose}
                contentLabel="Konfirmasi Hapus"
                ariaHideApp={false}
                style={customStyles}  // Menerapkan gaya kustom
            >
               <div className="flex flex-col items-center">
                <h2 className="text-xl font-semibold text-center mb-4">{message}</h2>
                <div className="flex gap-4">
                    <button
                        className="px-6 py-2 rounded-md bg-gray-300 text-gray-800 hover:bg-gray-400 transition-colors"
                        onClick={onRequestClose}
                    >
                        Batal
                    </button>
                    <button
                        className="px-6 py-2 rounded-md bg-red-600 text-white hover:bg-red-700 transition-colors"
                        onClick={() => {
                            onConfirm(id); // Mengonfirmasi penghapusan
                            onRequestClose(); // Menutup modal
                        }}
                    >
                        OK
                    </button>
                </div>
            </div>
                
            </Modal>
       
    );
};

export default DeleteConfirmationModal