<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Penduduk extends Model
{
    use HasFactory;

    protected $table = 'penduduks';
    protected $dates = ['deleted_at'];
    protected $fillable = [
        'nik',
        'nama',
        'lahir',
        'tanggal',
        'kelamin',
        'agama',
        'kawin',
        'pendidikan',
        'pekerjaan',
        'kewarganegaran',
        'alamat',
        'hubungan',
        'keluarga_id',
        'telepon',
        'darah'
    ];

    public function keluarga()
    {
        return $this->belongsTo(Keluarga::class);
    }
}
