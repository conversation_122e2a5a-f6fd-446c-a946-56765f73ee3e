import React from 'react'

const Card = ({jum<PERSON><PERSON><PERSON><PERSON><PERSON>, jumlah<PERSON><PERSON><PERSON><PERSON>, p<PERSON><PERSON><PERSON><PERSON>,rataRata<PERSON><PERSON>}) => {
  return (
    <div className="grid grid-cols-2 gap-4 sm:grid-cols-2 lg:grid-cols-4">
        {/* Card 1 */}
        <div className="bg-gray-200 dark:bg-gray-700 rounded-lg p-3 shadow">
            <h2 className="text-sm font-semibold text-gray-700 dark:text-gray-200">Ju<PERSON><PERSON></h2>
            <p className="mt-2 text-xl font-bold text-gray-900 dark:text-gray-100">{jumlahKeluarga}</p>
            <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">Terbaru</p>
        </div>

        {/* Card 2 */}
        <div className="bg-gray-200 dark:bg-gray-700 shadow rounded-lg p-3">
            <h2 className="text-sm font-semibold text-gray-700 dark:text-gray-200 mb-7 lg:mb-0"><PERSON><PERSON><PERSON></h2>
            <p className="mt-2 text-xl font-bold text-gray-900 dark:text-gray-100">{jumlahPenduduk}</p>
            <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">Terbaru</p>
        </div>

        {/* Card 3 */}
        <div className="bg-gray-200 dark:bg-gray-700 shadow rounded-lg p-3">
            <p className="text-sm font-semibold text-gray-700 dark:text-gray-200 mb-7 lg:mb-0">Memiliki Pekerjaan</p>
            <p className="mt-2 text-xl font-bold text-gray-900 dark:text-gray-100">{pekerjaan}</p>
            <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">Terbaru</p>    
        </div>

        {/* Card 4 */}
        <div className="bg-gray-200 dark:bg-gray-700 shadow rounded-lg p-3">
            <h2 className="text-sm font-semibold text-gray-700 dark:text-gray-200">Rata-rata Usia</h2>
            <p className="mt-2 text-xl font-bold text-gray-900 dark:text-gray-100">{rataRataUsia}</p>
            <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">Terbaru</p>
        </div>
    </div>
    )
}

export default Card