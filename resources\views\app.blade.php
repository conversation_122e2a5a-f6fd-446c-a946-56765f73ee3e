<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- Menggunakan title dinamis -->
    <title inertia>
        {{ config('app.name', 'Bale Mulia') . (isset($page['props']['title']) ? ' - ' . $page['props']['title'] : '') }}
    </title>

    <head>
    <link rel="icon" href="https://pbs.twimg.com/profile_images/344513261581086550/11f23533ea6c4a2027f1bff1b7fc68fa_400x400.png" type="image/png" class="favicon"" type="image/png">
    <!-- Tag lainnya -->
    </head>


    <!-- Scripts -->
    @routes
    @viteReactRefresh
    @vite(['resources/js/app.jsx', "resources/js/Pages/{$page['component']}.jsx"])
    @inertiaHead
</head>
<body class="font-sans antialiased">
    @inertia
</body>
</html>
