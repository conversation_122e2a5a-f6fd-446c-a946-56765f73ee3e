import React, { useState, useEffect } from 'react'
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout'
import { Head, router } from '@inertiajs/react';
import {usePage, Link} from '@inertiajs/react'
import DataTable from 'react-data-table-component';
import { MdDelete } from "react-icons/md";
import { MdModeEdit } from "react-icons/md";
import Modal from 'react-modal';
import DeleteConfirmationModal from '@/Components/Dashboard/DeleteModal';
import toast from 'react-hot-toast';
import DetailKK from '@/Components/Dashboard/DetailKK';

const KartuKeluarga = () => {
    const {keluargas} = usePage().props
    console.log(keluargas)
    const [isOpen, setIsOpen] = useState(false);
    const [selectedData, setSelectedData] = useState(null);
    const {props} = usePage();
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [deleteId, setDeleteId] = useState(null);
    useEffect(() => {
        if (props.message) {
          toast.success(props.message);
        }
        console.log(props.message);
      }, [props.message]);

    // Fungsi untuk membuka modal dengan data yang diklik
    const handleRowClick = (row) => {
        setSelectedData(row); // Simpan data item terpilih
        setIsOpen(true); // Buka modal
    };

    // Fungsi untuk menutup modal
    const closeModal = () => {
        setIsOpen(false);
        setSelectedData(null); // Reset data item terpilih
    };
    const columns = [
        {
            name: 'No. KK',
            selector: row => row.no_kk,
            cell: row => (
                <span 
                    className="text-blue-500 cursor-pointer truncate" 
                    onClick={() => handleRowClick(row)}
                >
                    {row.no_kk}
                </span>
            ), 
            sortable: true
        },
        {
            name: 'Kepala Keluarga',
            selector: row => row.kepala_keluarga,
            sortable: true
        },
        {
            name: 'Provinsi',
            selector: row => row.provinsi,
            sortable: true
        },
        {
            name: 'Kota/Kabupaten',
            selector: row => row.kabupaten,
            sortable: true
        },
        {
            name: 'Kecamamatan',
            selector: row => row.kecamatan,
            sortable: true
        },
        {
            name: 'Keluarahan/Desa',
            selector: row => row.desa,
            sortable: true
        },
        {
            name: 'Operations',
            cell: row => (
                <div className="flex space-x-2">
                    <button
                        onClick={() => handleEdit(row.id)}
                        className="px-2 py-1 text-white bg-blue-500 rounded hover:bg-blue-700"
                    >
                        <MdModeEdit/>
                    </button>
                    <button
                        onClick={() => handleDelete(row.id)}
                        className="px-2 py-1 text-white bg-red-500 rounded hover:bg-red-700"
                    >
                        <MdDelete/>
                    </button>
                    <DeleteConfirmationModal
                        isOpen={isModalOpen}
                        onRequestClose={() => setIsModalOpen(false)}
                        onConfirm={handleConfirmDelete}
                        id={deleteId}
                    />
                </div>
            ),
        },
    ];
    
    const handleEdit = (id) => {
        console.log('Edit item with id:', id);
        router.get(`/kartu-keluarga/${id}/edit`);
    };

    const handleDelete = (id) => {
        setDeleteId(id);
        setIsModalOpen(true); // Menampilkan modal konfirmasi
    };

    const handleConfirmDelete = (id) => {
        toast.promise(
            new Promise((resolve, reject) => {
                router.put(
                    `/kartu-keluarga/${id}/delete`,
                    {}, // Jika tidak ada data tambahan, berikan objek kosong
                    {
                        onSuccess: () => {
                            resolve(); // Tandai promise berhasil
                        },
                        onError: (error) => {
                            reject(error); // Tandai promise gagal
                        },
                    }
                );
            }),
            {
                success: 'Data berhasil dihapus!',
                error: (error) => `Terjadi kesalahan: ${error}`,
            }
        );
    };
    

        const paginationComponentOptions = {
            selectAllRowsItem: true,
            selectAllRowsItemText: "ALL"
        };
    
        const [searchText, setSearchText] = useState("");

        // Filter data berdasarkan pencarian
        const filteredData = keluargas.filter(item =>
            item.no_kk.toLowerCase().includes(searchText.toLowerCase()) ||
            item.kepala_keluarga.toLowerCase().includes(searchText.toLowerCase()) ||
            item.kabupaten.toLowerCase().includes(searchText.toLowerCase()) ||
            item.kecamatan.toLowerCase().includes(searchText.toLowerCase()) ||
            item.provinsi.toLowerCase().includes(searchText.toLowerCase())||
            item.desa.toLowerCase().includes(searchText.toLowerCase())
        );
  return (
    <AuthenticatedLayout
    header={
        <h2 className="text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200">
            Kartu Keluarga
        </h2>
    }
    >
        <Head title="Kartu Keluarga" />
        <div className="py-12">
                <div className="mx-auto max-w-4xl sm:px-6 lg:px-8">
                    <div className="overflow-hidden bg-white shadow-sm sm:rounded-lg dark:bg-gray-800">
                        <div className="p-6 text-gray-900 dark:text-gray-100">
                            <div className='flex justify-between gap-2 lg:gap-0'>
                                <div className="bg-blue-500 h-10 lg:w-[120px] lg:h-[35px] flex items-center justify-center text-white rounded-sm dark:bg-blue-600 hover:bg-blue-600 mb-2 lg:mb-5">
                                    <Link href={route('kk.create')}><span>tambah</span><span className='hidden lg:inline'> data</span></Link>
                                    
                                </div>
                                <input
                                    type="text"
                                    placeholder="Search..."
                                    value={searchText}
                                    onChange={(e) => setSearchText(e.target.value)}
                                    className="mb-4 p-2 border rounded bg-white text-gray-900 "
                                />
                            </div>
                            
                            <DataTable
                                columns={columns}
                                data={filteredData}
                                pagination
                                paginationComponentOptions={paginationComponentOptions}
                                paginationTotalRows={keluargas.length}
                                responsive
                                />
                            <Modal
                                isOpen={isOpen}
                                onRequestClose={closeModal}
                                contentLabel="Detail Data"
                                className="bg-customBlue rounded-lg p-4 shadow-lg w-full h-[90%] max-w-[70%] mx-auto z-50"
                                overlayClassName="bg-black bg-opacity-50 fixed inset-0 flex items-center justify-center z-40"
                            >
                                {selectedData && (
                                    <DetailKK selectedData={selectedData} closeModal={closeModal} />
                                )}
                            </Modal>


                        </div>
                    </div>
                </div>
            </div>
    </AuthenticatedLayout>
  )
}

export default KartuKeluarga

