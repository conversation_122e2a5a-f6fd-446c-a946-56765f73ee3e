import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout';
import { Head, router } from '@inertiajs/react';
import DataTable from 'react-data-table-component';
import { MdDelete } from "react-icons/md";
import { MdModeEdit } from "react-icons/md";
import { useState ,useEffect} from 'react';
import Modal from 'react-modal';
import { Link,usePage } from '@inertiajs/react';
import toast from 'react-hot-toast';
import DeleteConfirmationModal from '@/Components/Dashboard/DeleteModal';
import DetailKTP from '@/Components/Dashboard/DetailKTP';



export default function Penduduk() {
    const { penduduk } = usePage().props;
    const [isOpen, setIsOpen] = useState(false);
    const [selectedData, setSelectedData] = useState(null);
    const {props} = usePage();
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [deleteId, setDeleteId] = useState(null);
    useEffect(() => {
        if (props.message) {
          toast.success(props.message);
        }
        console.log(props.message);
      }, [props.message]);
    console.log("ini penduduk", penduduk);
    // Fungsi untuk membuka modal dengan data yang diklik
    const handleRowClick = (row) => {
        setSelectedData(row); // Simpan data item terpilih
        setIsOpen(true); // Buka modal
    };

    // Fungsi untuk menutup modal
    const closeModal = () => {
        setIsOpen(false);
        setSelectedData(null); // Reset data item terpilih
    };
    const columns = [
        {
            name: 'NIK',
            selector: row => row.nik,
            cell: row => (
                <span 
                    className="text-blue-500 cursor-pointer truncate" 
                    onClick={() => handleRowClick(row)}
                >
                    {row.nik}
                </span>
            ), 
            sortable: true
        },
        {
            name: 'Nama',
            selector: row => row.nama,
            sortable: true
        },
        {
            name: 'Jenis Kelamin',
            selector: row => row.kelamin,
            sortable: true
        },
        {
            name: 'Usia',
            selector: row => row.usia,
            sortable: true
        },
        {
            name: 'Agama',
            selector: row => row.agama,
            sortable: true
        },
        {
            name: 'Pekerjaan',
            selector: row => row.pekerjaan,
            sortable: true
        },
        {
            name: 'Operations',
            cell: row => (
                <div className="flex space-x-2">
                    <button
                        onClick={() => handleEdit(row.id)}
                        className="px-2 py-1 text-white bg-blue-500 rounded hover:bg-blue-700"
                    >
                        <MdModeEdit/>
                    </button>
                    <button
                        onClick={() => handleDelete(row.id)}
                        className="px-2 py-1 text-white bg-red-500 rounded hover:bg-red-700"
                    >
                        <MdDelete/>
                    </button>
                    <DeleteConfirmationModal
                        isOpen={isModalOpen}
                        onRequestClose={() => setIsModalOpen(false)}
                        onConfirm={handleConfirmDelete}
                        id={deleteId}
                    />
                </div>
            ),
        },
    ];
    
    const handleEdit = (id) => {
        console.log('Edit item with id:', id);
        router.get(`/penduduk/${id}/edit`);
    };

    const handleDelete = (id) => {
        setDeleteId(id);
        setIsModalOpen(true); // Menampilkan modal konfirmasi
    };

    const handleConfirmDelete = (id) => {
        // Menampilkan toast pertama
        toast.promise(
            new Promise((resolve, reject) => {
                router.put(`/penduduk/${id}/delete`,{}, {
                    onSuccess: () => {
                        resolve(); // Sukses, lanjut ke toast kedua
                    },
                    onError: (error) => {
                        reject(error); // Gagal, tampilkan error
                    }
                });
            }),
            {
                loading: 'Menghapus data...',
                success: 'Data berhasil dihapus!',
                error: (error) => `Terjadi kesalahan: ${error}`,
            }
        );
    };

        const paginationComponentOptions = {
            selectAllRowsItem: true,
            selectAllRowsItemText: "ALL"
        };
    
        const [searchText, setSearchText] = useState("");

        // Filter data berdasarkan pencarian
        const filteredData = penduduk.filter(item =>
            item.nik.toLowerCase().includes(searchText.toLowerCase()) ||
            item.nama.toLowerCase().includes(searchText.toLowerCase()) ||
            item.alamat.toLowerCase().includes(searchText.toLowerCase()) ||
            item.kelamin.toLowerCase().includes(searchText.toLowerCase())||
            item.pekerjaan.toLowerCase().includes(searchText.toLowerCase())||
            item.agama.toLowerCase().includes(searchText.toLowerCase())||
            item.tanggal.toLowerCase().includes(searchText.toLowerCase())||
            item.lahir.toLowerCase().includes(searchText.toLowerCase())
        );

    return (
        <AuthenticatedLayout
            header={
                <h2 className="text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200">
                    Penduduk
                </h2>
            }
        >
            <Head title="Penduduk" />

            <div className="py-0 lg:py-12">
                <div className="mx-auto max-w-4xl sm:px-6 lg:px-8">
                    <div className="overflow-hidden bg-white shadow-sm sm:rounded-lg dark:bg-gray-800">
                        <div className="p-6 text-gray-900 dark:text-gray-100">
                            <div className='flex justify-between gap-2'>
                                <div className='bg-blue-500 text-white py-2 px-4 rounded w-[150px] h-10'>
                                <Link className='text-white' href='/penduduk/create'>
                                    <span className='hidden sm:block'>Tambah Data</span>
                                    <span className='sm:hidden'>Tambah</span>
                                </Link>
                                </div>
                                 {/* Input search */}
                                <input
                                    type="text"
                                    placeholder="Search..."
                                    value={searchText}
                                    onChange={(e) => setSearchText(e.target.value)}
                                    className="mb-4 p-2 border rounded bg-white text-gray-900"
                                />

                            </div>
                            
                            <DataTable
                                columns={columns}
                                data={filteredData}
                                pagination
                                paginationComponentOptions={paginationComponentOptions}
                                paginationTotalRows={penduduk.length}
                                responsive
                            />
                            <Modal
                                isOpen={isOpen}
                                onRequestClose={closeModal}
                                contentLabel="Detail Data"
                                className="bg-customBlue rounded-lg p-4 shadow-lg w-full h-[459px] max-w-[927px] mx-auto z-50"
                                overlayClassName="bg-black bg-opacity-50 fixed inset-0 flex items-center justify-center z-40"
                            >
                                {selectedData && (
                                    <DetailKTP selectedData={selectedData} closeModal={closeModal} />
                                )}
                            </Modal>
                        </div>
                    </div>
                </div>
            </div>
        </AuthenticatedLayout>
    );
}
