import React from 'react'
import { Responsive<PERSON>ontainer, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, Cell } from 'recharts';

const UsiaChart = ({usiaData}) => {
  const colors = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', 'red'];
// 0-16, 17-25, 26-35, 36-45, 46-60, >=61
  const data = [
    {
      name: '1-16',
      jumlah: 4000,
    },
    {
      name: '17-25',
      jumlah: 3000,
    },
    {
      name: '26-35',
      jumlah: 2000,
    },
    {
      name: '36-45',
      jumlah: 2780,
    },
    {
      name: '46-60',
      jumlah: 1890,
    },
    {
      name: '>60',
      jumlah: 2390,
    },
  ];

  return (
    <ResponsiveContainer width="100%" height="100%">
      <BarChart
        data={usiaData}
        margin={{
          top: 20,
          right: 30,
          left: 20,
          bottom: 5,
        }}
      >
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="name" />
        <YAxis />
        <Tooltip />
        <Legend />
        <Bar dataKey="jumlah" fill="#8884d8">
          {data.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
          ))}
        </Bar>
      </BarChart>
    </ResponsiveContainer>
  )
}

export default UsiaChart;