import React, { useState } from 'react';
import { router,Head,usePage } from '@inertiajs/react';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout';
import toast from 'react-hot-toast';
import profesiOptions from '@/Data/Profesi.json';
import alamatOptions from '@/Data/Alamat.json';
import PhoneInput from 'react-phone-input-2'
import 'react-phone-input-2/lib/style.css'

const Create = () => {
    const {keluargas} = usePage().props
    console.log("Keluarga",keluargas)
  const [formData, setFormData] = useState({
    nik: '',
    nama: '',
    lahir: '',
    tanggal: '',
    kelamin: '',
    agama: '',
    kawin: '',
    pendidikan: '',
    pekerjaan: '',
    kewarganegaran: '',
    hubungan: '',
    darah: '',
    status: '',
    telepon: '',
    keluarga_id: '',
    alamat: '',
  });

  // Fungsi untuk mengubah nilai input form
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  };

  // Fungsi untuk mengirim data form

const handleSubmit = (e) => {
    e.preventDefault();

    if (
      !formData.nama || 
      !formData.lahir || 
      !formData.tanggal || 
      !formData.kelamin || 
      !formData.agama ||
      !formData.pendidikan || 
      !formData.pekerjaan || 
      !formData.kewarganegaran || 
      !formData.keluarga_id ||
      !formData.darah ||  
      !formData.alamat || 
      !formData.nik
  ) {
      toast.error('Semua field harus diisi!');
      return;
  } else if (formData.nik.length !== 16) {
      toast.error('NIK harus 16 karakter!');
      return;
  } else if (!/^\d+$/.test(formData.nik)) { // Validasi angka menggunakan regex
      toast.error('NIK harus berupa angka!');
      return;
  }
    router.post('/penduduk', formData, {
        onSuccess: () => {
            toast.success('Data berhasil disimpan!');
            router.visit('/penduduk'); // Redirect setelah sukses
        },
        onError: (errors) => {
            console.log('Error occurred:', errors);
            toast.error('Terjadi kesalahan saat menyimpan data!');
        },
    });
    console.log(formData)
};

const selectedKeluarga = keluargas.find(item => item.id === parseInt(formData.keluarga_id, 10));
console.log("selectedKeluarga",selectedKeluarga)
console.log("formData.keluarga_id",formData.keluarga_id)
console.log("keluarga",keluargas)
  return (
    <AuthenticatedLayout
    header={
        <h2 className="text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200">
            Penduduk
        </h2>
    }
    >
    <Head title="Tambah Penduduk" />
    <div className='bg-gray-100 dark:bg-gray-900'>
    <div className="bg-white p-6 rounded-lg shadow-lg max-w-3xl mx-auto dark:bg-gray-700 mt-10">
      <h2 className="text-xl font-bold mb-4 dark:text-white text-center">Tambah Data Penduduk</h2>
      <form onSubmit={handleSubmit}>
        <div className="mb-4">
          <label className="block text-sm font-semibold dark:text-gray-100" htmlFor="nik">
            NIK <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            id="nik"
            name="nik"
            value={formData.nik}
            onChange={handleInputChange}
            maxLength={16}
            className="mt-1 p-2 border border-gray-300 rounded w-full dark:bg-gray-200 dark:border-gray-200 dark:text-gray-900"
          
          />
        </div>

        <div className="mb-4">
          <label className="block text-sm font-semibold dark:text-gray-100" htmlFor="nama">
            Nama <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            id="nama"
            name="nama"
            value={formData.nama}
            onChange={handleInputChange}
            className="mt-1 p-2 border border-gray-300 rounded w-full dark:bg-gray-200 dark:border-gray-200 dark:text-gray-900"
          
          />
        </div>
        <div className='flex mb-4 gap-3'>
            <div className="w-1/2">
                <label className="block text-sm font-semibold dark:text-gray-100"  htmlFor="lahir">
                    Tempat Lahir <span className="text-red-500">*</span>
                </label>
                <input
                type="text"
                id="lahir"
                name="lahir"
                value={formData.lahir}
                onChange={handleInputChange}
                className="mt-1 p-2 border border-gray-300 rounded w-full dark:bg-gray-200 dark:border-gray-200 dark:text-gray-900  "
              
                />
            </div>
            <div className='w-1/2'>
                <label className="block text-sm font-semibold dark:text-gray-100" htmlFor="tanggal">
                    Tanggal Lahir <span className="text-red-500">*</span>
                </label>
                <input
                    type="date"
                    id="tanggal"
                    name="tanggal"
                    value={formData.tanggal}
                    onChange={handleInputChange}
                    className="mt-1 p-2 border border-gray-300 rounded w-full dark:bg-gray-200  dark:border-gray-200 dark:text-gray-900"
                  
                />
            </div>
        </div>
            <div className='mb-4 flex gap-3'>
                <div className="w-1/2">
                    <label className="block text-sm font-semibold dark:text-gray-100" htmlFor="kelamin">
                        Jenis Kelamin <span className="text-red-500">*</span>
                    </label>
                    <select
                        id="kelamin"
                        name="kelamin"
                        value={formData.kelamin}
                        onChange={handleInputChange}
                        className="mt-1 p-2 border border-gray-300 rounded w-full dark:bg-gray-200 dark:border-gray-200 dark:text-gray-900"
                    
                    >
                        <option value="">Pilih Jenis Kelamin</option>
                        <option value="Laki-laki">Laki-laki</option>
                        <option value="Perempuan">Perempuan</option>
                    </select>
                </div>
                <div className="w-1/2">
                    <label className="block text-sm font-semibold dark:text-gray-100" htmlFor="agama">
                        Agama <span className="text-red-500">*</span>
                    </label>
                    <select
                            id="agama"
                            name="agama"
                            value={formData.agama}
                            onChange={handleInputChange}
                            className="mt-1 p-2 border border-gray-300 rounded w-full dark:bg-gray-200 dark:border-gray-200 dark:text-gray-900"
                        
                        >
                            <option value="">Pilih Agama</option>
                            <option value="Islam">Islam</option>
                            <option value="Kristen Protestan">Kristen Protestan</option>
                            <option value="Katolik">Katolik</option>
                            <option value="Hindu">Hindu</option>
                            <option value="Budha">Budha</option>
                            <option value="Konghucu">Konghucu</option>
                        </select>
                </div>
            </div>
            
        
        <div className='flex mb-4 gap-3'>
        <div className="w-1/2">
          <label className="block text-sm font-semibold dark:text-gray-100" htmlFor="pendidikan">
            Pendidikan <span className="text-red-500">*</span>
          </label>
          <select
                id="pendidikan"
                name="pendidikan"
                value={formData.pendidikan}
                onChange={handleInputChange}
                className="mt-1 p-2 border border-gray-300 rounded w-full dark:bg-gray-200 dark:border-gray-200 dark:text-gray-900"
              
            >
                <option value="">Pilih Pendidikan</option>
                <option value="Tidak/Belum Sekolah">Tidak/Belum Sekolah</option>
                <option value="Belum Tamat SD/Sederajat">Belum Tamat SD/Sederajat</option>
                <option value="Tamat SD/Sederajat">Tamat SD/Sederajat</option>
                <option value="SLTP/Sederajat">SLTP/Sederajat</option>
                <option value="SLTA/Sederajat">SLTA/Sederajat</option>
                <option value="Diploma I/II">Diploma I/II</option>
                <option value="Akademi/Diploma III/S.MUDA">Akademi/Diploma III/S.MUDA</option>
                <option value="Diploma IV/Strata I">Diploma IV/Strata I</option>
                <option value="Strata II">Strata II</option>
                <option value="Strata III">Strata III</option>
            </select>
        </div>

        <div className="w-1/2">
          <label className="block text-sm font-semibold dark:text-gray-100" htmlFor="pekerjaan">
            Pekerjaan <span className="text-red-500">*</span>
          </label>
          <select
                id="pekerjaan"
                name="pekerjaan"
                value={formData.pekerjaan}
                onChange={handleInputChange}
                className="mt-1 p-2 border border-gray-300 rounded w-full dark:bg-gray-200 dark:border-gray-200 dark:text-gray-900"
              
            >
                <option value="">Pilih Pekerjaan</option>
                {profesiOptions.map((item) => (
                <option key={item.id} value={item.profesi}>
                    {item.profesi}
                </option>
    ))}
            </select>
        </div>
        </div>
        <div className='flex mb-4 gap-3'>
            <div className="w-1/2">
            <label className="block text-sm font-semibold dark:text-gray-100" htmlFor="darah">
                Golongan Darah <span className="text-red-500">*</span>
            </label>
            <select
                id="darah"
                name="darah"
                value={formData.darah}
                onChange={handleInputChange}
                className="mt-1 p-2 border border-gray-300 rounded w-full dark:bg-gray-200 dark:border-gray-200 dark:text-gray-900" 
              
                >
                <option value="">Pilih Golongan Darah</option>
                <option value="A">A</option>
                <option value="B">B</option>
                <option value="AB">AB</option>
                <option value="O">O</option>
            </select>
            </div>

            <div className="w-1/2 ">
                <label className="block text-sm font-semibold dark:text-gray-100" htmlFor="telepon">
                    Telepon
                </label>
                <PhoneInput
                country="id" // Default negara Indonesia
                placeholder="Masukkan nomor telepon"
                value={formData.telepon} // Nilai input telepon
                onChange={(phone) => setFormData({ ...formData, telepon: phone })} // Fungsi perubahan nilai
                enableSearch={true} // Aktifkan pencarian negara
                containerClass='p-1 mt-1 border border-gray-300 rounded w-full dark:bg-gray-200 dark:border-gray-200 dark:text-gray-900'
                inputClass='border border-gray-300 rounded w-full dark:bg-gray-200 dark:border-gray-200 dark:text-gray-900'
                dropdownClass='dark:bg-gray-200 dark:border-gray-200 dark:text-gray-900'
                buttonClass='dark:bg-gray-200 dark:border-gray-200 dark:text-gray-900'
                />
            </div>
        </div>
        <div className='mb-4'>
            <label className="block text-sm font-semibold dark:text-gray-100" htmlFor="keluarga_id">
                    Keluarga <span className="text-red-500">*</span>
            </label>
                <select
                    id="keluarga_id"
                    name="keluarga_id"
                    value={formData.keluarga_id}
                    onChange={handleInputChange}
                    className="mt-1 p-2 border border-gray-300 rounded w-full dark:bg-gray-200 dark:border-gray-200 dark:text-gray-900" 
                
                    >
                    <option value="">Pilih Nomer Kartu Keluarga - Kepala Keluarga</option>
                    {keluargas
                    .filter(item => item.no_kk === '0000000000000000') // Filter untuk mengecualikan no_kk yang bernilai '0000000000000000'
                    .map((item) => (
                        <option key={item.id} value={item.id}>
                        -
                        </option>
                    ))}
                    {keluargas
                    .filter(item => item.no_kk !== '0000000000000000') // Filter untuk mengecualikan no_kk yang bernilai '0000000000000000'
                    .map((item) => (
                        <option key={item.id} value={item.id}>
                        {item.no_kk} - {item.kepala_keluarga}
                        </option>
                    ))
                    }
                </select>
        </div>
        {/* Render conditional untuk hubungan dan kawin */}
        {selectedKeluarga?.no_kk !== '0000000000000000' && (
            <div className="mb-4 flex gap-3">
                <div className="w-1/2">
                    <label className="block text-sm font-semibold dark:text-gray-100" htmlFor="hubungan">
                        Status Hubungan Dalam Keluarga
                    </label>
                    <select
                        id="hubungan"
                        name="hubungan"
                        value={formData.hubungan}
                        onChange={handleInputChange}
                        className="mt-1 p-2 border border-gray-300 rounded w-full dark:bg-gray-200 dark:border-gray-200 dark:text-gray-900"
                    >
                        <option value="">Pilih Status Hubungan Dalam Keluarga</option>
                        <option value="Kepala Keluarga">Kepala Keluarga</option>
                        <option value="Suami">Suami</option>
                        <option value="Istri">Istri</option>
                        <option value="Anak">Anak</option>
                        <option value="Menantu">Menantu</option>
                        <option value="Cucu">Cucu</option>
                        <option value="Orangtua">Orangtua</option>
                        <option value="Mertua">Mertua</option>
                        <option value="Famili Lain">Famili Lain</option>
                        <option value="Lainnya">Lainnya</option>
                    </select>
                </div>
                <div className="w-1/2">
                    <label className="block text-sm font-semibold dark:text-gray-100" htmlFor="kawin">
                        Status Perkawinan
                    </label>
                    <select
                        id="kawin"
                        name="kawin"
                        value={formData.kawin}
                        onChange={handleInputChange}
                        className="mt-1 p-2 border border-gray-300 rounded w-full dark:bg-gray-200 dark:border-gray-200 dark:text-gray-900"
                    >
                        <option value="">Pilih Status Perkawinan</option>
                        <option value="Belum Kawin">Belum Kawin</option>
                        <option value="Kawin">Kawin</option>
                        <option value="Cerai Hidup">Cerai Hidup</option>
                        <option value="Cerai Mati">Cerai Mati</option>
                    </select>
                </div>
            </div>
        )}
        <div className="mb-4">
          <label className="block text-sm font-semibold dark:text-gray-100" htmlFor="kewarganegaran">
            Kewarganegaraan <span className="text-red-500">*</span>
          </label>
          <select
                            id="kewarganegaran"
                            name="kewarganegaran"
                            value={formData.kewarganegaran}
                            onChange={handleInputChange}
                            className="mt-1 p-2 border border-gray-300 rounded w-full dark:bg-gray-200 dark:border-gray-200 dark:text-gray-900"
                        
                        >
                            <option value="">Pilih Kewarganegaraan</option>
                            <option value="WNI">WNI</option>
                            <option value="WNA">WNA</option>
                        </select>
        </div>
        <div className="mb-4">
          <label className="block text-sm font-semibold dark:text-gray-100" htmlFor="alamat">
            Alamat Rumah <span className="text-red-500">*</span>
          </label>
          <select
                id="alamat"
                name="alamat"
                value={formData.alamat}
                onChange={handleInputChange}
                className="mt-1 p-2 border border-gray-300 rounded w-full dark:bg-gray-200 dark:border-gray-200 dark:text-gray-900"
              
            >
                <option value="">Alamat Rumah</option>
                {alamatOptions.map((item) => (
                <option key={item.id} value={item.jalan}>
                    {item.jalan}
                </option>
    ))}
            </select>
        </div>
        
        <button
          type="submit"
          className="mt-4 px-4 py-2 bg-blue-500 text-white dark:text-gray-100 rounded-lg"
        >
          Simpan
        </button>
      </form>
    </div>
    </div>
    </AuthenticatedLayout>
  );
};

export default Create;