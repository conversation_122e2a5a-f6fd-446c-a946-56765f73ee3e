<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\PendudukController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\DashboardController;
use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return redirect()->route('login');
});


Route::get('/dashboard', function () {
    return Inertia::render('Dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

use App\Http\Controllers\KeluargaController;
use App\Models\Penduduk;

Route::middleware(['auth', 'verified'])->group(function () {
    // Halaman utama daftar penduduk
    Route::get('/trash-keluarga', [KeluargaController::class, 'showTrashed'])->name('trash.keluarga');
    Route::delete('/trash-keluarga/{id}/force', [KeluargaController::class, 'destroyTrashed'])->name('trash.keluarga.destroy');
    Route::get('/trash-keluarga/{id}/restore', [KeluargaController::class, 'restoreTrashed'])->name('trash.keluarga.restore');
});
Route::middleware(['auth', 'verified'])->group(function () {
    // Halaman utama daftar penduduk
    Route::get('/trash-penduduk', [PendudukController::class, 'showTrashed'])->name('trash.penduduk');
    Route::delete('/trash-penduduk/{id}/force', [PendudukController::class, 'destroyTrashed'])->name('trash.penduduk.destroy');
    Route::get('/trash-penduduk/{id}/restore', [PendudukController::class, 'restoreTrashed'])->name('trash.penduduk.restore');
});

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('/kartu-keluarga', [KeluargaController::class, 'index'])->name('kk.index');
    Route::get('/kartu-keluarga/create', [KeluargaController::class, 'create'])->name('kk.create');
    Route::post('/kartu-keluarga', [KeluargaController::class, 'store'])->name('kk.store');
    Route::get('/kartu-keluarga/{keluarga}/edit', [KeluargaController::class, 'edit'])->name('kk.edit');
    Route::put('/kartu-keluarga/{keluarga}', [KeluargaController::class, 'update'])->name('kk.update');
    Route::put('/kartu-keluarga/{id}/delete', [KeluargaController::class, 'destroy'])->name('kk.destroy');

});


Route::middleware(['auth', 'verified'])->group(function () {
    // Halaman utama daftar penduduk
    Route::get('/penduduk', [PendudukController::class, 'index'])->name('penduduk');

    // Tambahkan route CRUD untuk penduduk
    Route::get('/penduduk/create', [PendudukController::class, 'create'])->name('penduduk.create');
    Route::post('/penduduk', [PendudukController::class, 'store'])->name('penduduk.store');
    Route::get('/penduduk/{penduduk}/edit', [PendudukController::class, 'edit'])->name('penduduk.edit');
    Route::put('/penduduk/{penduduk}', [PendudukController::class, 'update'])->name('penduduk.update');
    Route::put('/penduduk/{id}/delete', [PendudukController::class, 'destroy'])->name('penduduk.destroy');
});



// Route::middleware(['auth', 'verified'])->prefix('trash')->group(function () {
//     Route::get('/keluarga', [TrashController::class, 'indexKeluarga'])->name('trash.keluarga');
//     Route::delete('/keluarga/{keluarga}', [TrashController::class, 'destroyKeluarga'])->name('trash.keluarga.destroy');
// });


Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});
Route::middleware('auth')->group(function () {
    Route::get('/user', [UserController::class, 'index'])->name('user.index');
    Route::get('/user/create', [UserController::class, 'register'])->name('user.create');
    Route::post('/user', [UserController::class, 'store'])->name('user.store');
    Route::delete('/user/{user}', [UserController::class, 'destroy'])->name('user.destroy');
});

Route::get('/dashboard', [DashboardController::class, 'jumlahPenduduk'])
    ->middleware(['auth', 'verified'])
    ->name('dashboard');


require __DIR__.'/auth.php';
