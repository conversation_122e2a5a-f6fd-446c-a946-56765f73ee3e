<?php

namespace App\Http\Controllers;

use App\Models\Keluarga;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class KeluargaController extends Controller
{
    public function index()
    {
        $keluargas = Keluarga::with('penduduk')
        ->where('no_kk', '!=', '0000000000000000')
        ->where('deleted_at', null) 
        ->get();

        return Inertia::render('KK/Index', [
            'keluargas' => $keluargas,
        ]);
    }

    public function create()
    {
        return Inertia::render('KK/Create');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'no_kk' => 'required|string|unique:keluargas,no_kk',
            'kepala_keluarga' => 'required|string',
            'provinsi' => 'required|string',
            'kabupaten' => 'required|string',
            'kecamatan' => 'required|string',
            'desa' => 'required|string',
            'rt' => 'required|string',
            'rw' => 'required|string',
            'alamat' => 'required|string',
            'kode_pos' => 'required|string',
        ]);

        Keluarga::create($validated);

        return redirect()->route('kk.index')->with('success', 'Keluarga berhasil ditambahkan.');
    }

    public function show(Keluarga $keluarga)
    {
        return Inertia::render('KK/Show', [
            'keluarga' => $keluarga->load('penduduk'),
        ]);
    }

    public function edit(Keluarga $keluarga)
    {
        return Inertia::render('KK/Edit', [
            'keluarga' => $keluarga,
        ]);
    }

    public function update(Request $request, Keluarga $keluarga)
    {
        $validated = $request->validate([
            'no_kk' => 'required|string|unique:keluargas,no_kk,' . $keluarga->id,
            'kepala_keluarga' => 'required|string',
            'provinsi' => 'required|string',
            'kabupaten' => 'required|string',
            'kecamatan' => 'required|string',
            'desa' => 'required|string',
            'rt' => 'required|string',
            'rw' => 'required|string',
            'alamat' => 'required|string',
            'kode_pos' => 'required|string',
        ]);

        $keluarga->update($validated);

        return redirect()->back()->with('message', 'Data berhasil diperbarui!');
    }

    public function destroy($id)
    {
        // Update kolom `deleted_at` pada tabel `keluargas` menggunakan query builder
        DB::table('keluargas')
            ->where('id', $id)
            ->update(['deleted_at' => now()]);
        
        DB::table('penduduks')
            ->where('keluarga_id', $id)
            ->update(['deleted_at' => now()]);
    
        return redirect()->route('kk.index')->with('success', 'Keluarga berhasil dihapus.');
    }
    
    
    public function showTrashed(){
        $keluargas = Keluarga::with('penduduk')
        ->where('no_kk', '!=', '0000000000000000')
        ->whereNotNull('deleted_at')
        ->get();
        return Inertia::render('Trash/KeluargaTrash', [
            'keluargas' => $keluargas
        ]);
    }

    public function destroyTrashed($id)
    {
        // Hapus keluarga secara paksa
        DB::table('keluargas')->where('id', $id)->delete();

        return redirect()->route('trash.keluarga')->with('success', 'Keluarga berhasil dihapus.');
    }



    public function restoreTrashed($id)
    {
        // Update kolom `deleted_at` menjadi NULL
        DB::table('keluargas')
            ->where('id', $id)
            ->update(['deleted_at' => null]);
        
        DB::table('penduduks')
            ->where('keluarga_id', $id)
            ->update(['deleted_at' => null]);

        return redirect('/trash-keluarga')->with('success', 'Keluarga has been restored');
    }

}
